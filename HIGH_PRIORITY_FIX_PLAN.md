# 🔧 高優先級問題修復計劃

## 📋 修復概覽

本計劃將按順序修復以下三個高優先級問題：
1. **統一路由註冊方式** - 解決 `/api` 路徑衝突
2. **修復數據庫字段引用錯誤** - 確保查詢正常工作  
3. **統一角色定義** - 確保前後端一致

---

## 🎯 修復任務 1: 統一路由註冊方式

### 問題描述
在 `backend/server.js` 中，多個路由使用相同的 `/api` 基礎路徑，可能導致路由衝突：
```javascript
app.use('/api', require('./routes/vocabulary-lists'));
app.use('/api', require('./routes/student-progress'));
```

### 修復步驟

#### 步驟 1.1: 檢查當前路由註冊
```javascript
// 當前問題代碼位置: backend/server.js 第68-76行
```

#### 步驟 1.2: 重新設計路由結構
**新的路由組織方案**：
```javascript
// 認證相關
app.use('/api/auth', require('./routes/auth'));

// 學生功能 - 統一前綴
app.use('/api/student', require('./routes/students'));
app.use('/api/student', require('./routes/practice'));

// 教師功能 - 統一前綴  
app.use('/api/teacher', require('./routes/vocabulary-lists'));
app.use('/api/teacher', require('./routes/student-progress'));

// 管理員功能 - 預留
// app.use('/api/admin', require('./routes/admin'));
```

#### 步驟 1.3: 修改 vocabulary-lists.js 路由
**需要修改的端點**：
- `/student/vocabulary-lists` → `/vocabulary-lists` (因為已有 `/api/teacher` 前綴)
- `/teacher/vocabulary-lists` → `/vocabulary-lists`
- `/teacher/vocabulary-lists/:listId` → `/vocabulary-lists/:listId`
- 其他相關端點

#### 步驟 1.4: 修改 student-progress.js 路由  
**需要修改的端點**：
- `/teacher/students-progress` → `/students-progress`
- `/teacher/student-progress/:studentId` → `/student-progress/:studentId`

#### 步驟 1.5: 更新前端API調用
**需要檢查和更新的文件**：
- `frontend/src/stores/auth.js`
- `frontend/src/views/teacher/*.vue`
- `frontend/src/views/student/*.vue`

---

## 🎯 修復任務 2: 修復數據庫字段引用錯誤

### 問題描述
在某些API查詢中引用了 `vocabularies` 表中不存在的 `grade` 字段。

### 修復步驟

#### 步驟 2.1: 檢查數據庫表結構
```sql
-- 執行檢查命令
PRAGMA table_info(vocabularies);
```

#### 步驟 2.2: 查找所有錯誤的字段引用
**需要檢查的文件**：
- `backend/routes/vocabulary-lists.js`
- `backend/routes/students.js`
- `backend/routes/practice.js`
- `backend/scripts/*.js`

**常見錯誤模式**：
```sql
-- 錯誤: 引用不存在的 grade 字段
SELECT v.*, v.grade FROM vocabularies v

-- 正確: 從關聯表獲取 grade 信息
SELECT v.*, vl.grade FROM vocabularies v 
JOIN vocabulary_list_items vli ON v.id = vli.vocabulary_id
JOIN vocabulary_lists vl ON vli.list_id = vl.id
```

#### 步驟 2.3: 修復所有錯誤查詢
**修復原則**：
- 如果需要 grade 信息，從 `vocabulary_lists` 表獲取
- 如果需要用戶 grade 信息，從 `users` 表獲取
- 移除對 `vocabularies.grade` 的直接引用

---

## 🎯 修復任務 3: 統一角色定義

### 問題描述
不同文件中的角色定義不一致：
- `Reference/PROJECT_SPECIFICATION.md`: 只有 `student`, `teacher`
- `backend/database/init.sql`: 有 `student`, `teacher`, `admin`

### 修復步驟

#### 步驟 3.1: 確定標準角色定義
**統一標準**：支持三種角色 `student`, `teacher`, `admin`

#### 步驟 3.2: 更新規格文檔
**需要更新的文件**：
- `Reference/PROJECT_SPECIFICATION.md`
- `Stage1/DETAILED_DEVELOPMENT_GUIDE.md`
- 其他相關文檔

#### 步驟 3.3: 檢查前端路由配置
**確保路由支持所有角色**：
```javascript
// frontend/src/router/index.js
// 確保有完整的 admin 路由配置
```

#### 步驟 3.4: 檢查中間件配置
**確保認證中間件支持所有角色**：
```javascript
// backend/middleware/auth.js
// 確保 requireRole 函數支持 admin 角色
```

---

## 🔄 修復執行順序

### 階段 1: 準備工作 (5分鐘)
1. 備份當前代碼 ✅ (已完成)
2. 檢查數據庫當前狀態
3. 記錄當前API端點

### 階段 2: 路由修復 (15分鐘)
1. 修改 `backend/server.js` 路由註冊
2. 更新 `vocabulary-lists.js` 端點路徑
3. 更新 `student-progress.js` 端點路徑
4. 測試後端路由

### 階段 3: 數據庫查詢修復 (10分鐘)
1. 檢查所有SQL查詢
2. 修復字段引用錯誤
3. 測試數據庫查詢

### 階段 4: 角色定義統一 (5分鐘)
1. 更新文檔
2. 檢查代碼一致性
3. 驗證角色權限

### 階段 5: 前端更新 (10分鐘)
1. 更新API調用路徑
2. 測試前端功能
3. 驗證完整流程

---

## ✅ 驗證清單

### 路由驗證
- [ ] 所有API端點可正常訪問
- [ ] 沒有路由衝突
- [ ] 前端API調用正常

### 數據庫驗證  
- [ ] 所有SQL查詢執行成功
- [ ] 沒有字段引用錯誤
- [ ] 數據返回正確

### 角色驗證
- [ ] 三種角色定義一致
- [ ] 權限控制正常工作
- [ ] 路由權限正確

---

## 📝 修復記錄

### 已完成任務
- [ ] 任務1: 統一路由註冊方式
- [ ] 任務2: 修復數據庫字段引用錯誤
- [ ] 任務3: 統一角色定義

### 修復過程記錄
*此處記錄修復過程中的重要變更和注意事項*

---

**修復狀態**: 🟡 準備中
**最後更新**: 2025-08-02
**負責人**: AI Assistant


