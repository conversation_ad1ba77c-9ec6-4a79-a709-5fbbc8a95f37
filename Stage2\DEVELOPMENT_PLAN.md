# 英語詞彙學習工具 v3 - Stage 2 開發計劃 (更新版)

## 📋 專案概述

### 當前狀況分析
基於詳細閱讀 Reference 文檔和 Stage1 完成狀況：

- **Stage 1 已完成** (100%):
  - ✅ 基礎架構：Node.js + Express + Vue.js + SQLite
  - ✅ 認證系統：JWT認證，角色權限控制
  - ✅ 響應式設計：TailwindCSS，支援桌面/平板/手機
  - ✅ 用戶界面：登入頁面，學生/教師/管理員儀表板
  - ✅ 數據庫：13個測試用戶，完整表結構
  - ✅ 路由系統：Vue Router，路由守衛，404處理

- **Stage 2 目標** (已完成基礎功能，需要架構升級):
  - ✅ 詞彙學習系統：詞彙卡片、語音播放、學習進度
  - ✅ 練習系統：多選題、填空題、拼寫練習、即時反饋
  - ✅ 進度管理：學習統計、錯誤複習、個人報告
  - 🔄 **架構升級需求**：詞彙列表管理系統

### 🚨 **重要架構問題識別**
經過用戶反饋，發現當前系統存在重大設計缺陷：

#### **問題1：缺少詞彙列表概念**
- **現狀**：詞彙直接分配給學生，沒有列表(list/set)概念
- **問題**：教師無法管理多個主題詞彙列表
- **影響**：無法實現"節日主題"、"中一上學期"等分類管理

#### **問題2：學習界面設計不完整**
- **現狀**：直接進入詞彙卡片學習
- **問題**：學生無法選擇要學習的詞彙列表
- **影響**：用戶體驗不符合教學實際需求

#### **問題3：數據管理方式落後**
- **現狀**：使用JSON文件+命令行腳本
- **問題**：教師無法通過Web界面管理詞彙
- **影響**：系統實用性大幅降低

### 開發階段重新定位
- **Stage 2.1** (已完成): 基礎學習功能實現
- **Stage 2.2** (當前階段): 詞彙列表管理系統架構升級 ← **我們在這裡**
- **Stage 2.3** (下一階段): 教師端詞彙管理界面

## 🎯 Stage 2.2 具體目標：詞彙列表管理系統

### 🔄 **架構升級目標**

#### **階段1：數據庫架構擴展**
- [x] **新增詞彙列表表結構** ✅ 已完成
  - `vocabulary_lists` - 詞彙列表主表
  - `vocabulary_list_items` - 詞彙列表內容表
  - `user_vocabulary_lists` - 學生詞彙列表分配表

- [x] **數據遷移腳本** ✅ 已完成
  - 將現有詞彙數據適配到新架構
  - 保持現有學習進度數據完整性

#### **階段2：學生界面重構**
- [x] **詞彙學習流程重新設計** ✅ 已完成
  - 詞彙列表選擇頁面（新增）
  - 修改現有詞彙卡片學習頁面
  - 列表間導航功能

#### **階段3：教師管理界面**
- [x] **詞彙列表管理** ✅ 已完成
  - 創建/編輯/刪除詞彙列表
  - ZIP文件上傳功能（images + JSON）
  - 詞彙列表預覽和管理

- [x] **學生分配管理** ✅ 已完成
  - 選擇學生/班級進行分配
  - 設置學習期限和要求
  - 批量分配操作

### ✅ **已完成功能（Stage 2.1）**
- ✅ **詞彙學習系統**：詞彙卡片、語音播放、學習進度
- ✅ **練習系統**：多選題、填空題、拼寫練習、即時反饋、結果記錄
- ✅ **進度管理**：學習統計、錯誤複習、個人報告
- ✅ **問題修復**：練習系統狀態重置問題、響應式佈局問題

## 🏗️ 技術架構

### 現有基礎（Stage 1 + Stage 2.1 已完成）
```
Frontend (Vue.js + TailwindCSS) ←→ Backend (Node.js + Express) ←→ SQLite Database
         ↓                                    ↓                        ↓
    路由系統 + 認證                      JWT中間件                  用戶表 + 基礎結構
         ↓                                    ↓                        ↓
    學習功能頁面                        學習API端點                詞彙和練習表
```

### Stage 2.2 架構升級：詞彙列表管理系統
```
新的數據庫架構：
┌─────────────────┐    ┌──────────────────────┐    ┌─────────────────┐
│ vocabulary_lists│    │vocabulary_list_items │    │   vocabularies  │
│ (詞彙列表主表)   │────│   (列表內容表)       │────│   (詞彙主表)    │
└─────────────────┘    └──────────────────────┘    └─────────────────┘
         │                                                    │
         │              ┌──────────────────────┐              │
         └──────────────│user_vocabulary_lists │──────────────┘
                        │  (學生列表分配表)     │
                        └──────────────────────┘
                                   │
                        ┌──────────────────────┐
                        │ user_vocabularies    │
                        │ (個別詞彙學習狀態)    │
                        └──────────────────────┘

新的學習流程：
學生登入 → 詞彙列表選擇頁面 → 選擇列表 → 詞彙卡片學習 → 練習系統
    ↓              ↓              ↓           ↓           ↓
 認證檢查    → 獲取分配列表 → 獲取列表詞彙 → 學習進度 → 練習記錄

教師管理流程：
教師登入 → 詞彙列表管理 → 創建/編輯列表 → 上傳ZIP文件 → 分配給學生
    ↓           ↓            ↓            ↓           ↓
 認證檢查 → 列表CRUD操作 → 文件處理 → 圖片存儲 → 批量分配
```

## �️ 數據庫架構設計

### 新增表結構設計

#### 1. 詞彙列表主表 (vocabulary_lists)
```sql
CREATE TABLE vocabulary_lists (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,                    -- 列表名稱 (如: "中一上學期詞彙", "節日主題詞彙")
    description TEXT,                      -- 列表描述
    grade TEXT,                           -- 適用年級 (S1, S2, S3)
    subject TEXT,                         -- 主題分類 (如: "節日", "食物", "運動")
    total_words INTEGER DEFAULT 0,        -- 詞彙總數
    created_by INTEGER NOT NULL,          -- 創建者（教師ID）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

#### 2. 詞彙列表內容表 (vocabulary_list_items)
```sql
CREATE TABLE vocabulary_list_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    list_id INTEGER NOT NULL,
    vocabulary_id INTEGER NOT NULL,
    order_index INTEGER DEFAULT 0,        -- 在列表中的順序
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (list_id) REFERENCES vocabulary_lists(id) ON DELETE CASCADE,
    FOREIGN KEY (vocabulary_id) REFERENCES vocabularies(id) ON DELETE CASCADE,
    UNIQUE(list_id, vocabulary_id)
);
```

#### 3. 學生詞彙列表分配表 (user_vocabulary_lists)
```sql
CREATE TABLE user_vocabulary_lists (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    list_id INTEGER NOT NULL,
    assigned_by INTEGER NOT NULL,         -- 分配者（教師ID）
    assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    due_date DATETIME,                    -- 完成期限
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'archived')),
    progress_percentage INTEGER DEFAULT 0, -- 整體完成百分比
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (list_id) REFERENCES vocabulary_lists(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id),
    UNIQUE(user_id, list_id)
);
```

### 現有表結構保持不變
- ✅ `users` - 用戶表
- ✅ `vocabularies` - 詞彙主表
- ✅ `user_vocabularies` - 個別詞彙學習狀態
- ✅ `practice_records` - 練習記錄表

### 數據關係圖
```
vocabulary_lists (詞彙列表)
    ↓ (1:N)
vocabulary_list_items (列表內容)
    ↓ (N:1)
vocabularies (詞彙主表)
    ↓ (1:N)
user_vocabularies (學習狀態)
    ↓ (N:1)
users (學生)
    ↓ (1:N)
user_vocabulary_lists (列表分配)
    ↓ (N:1)
vocabulary_lists (回到列表)
```

### Stage 2 數據準備方案

#### 統一JSON文件結構（推薦）
基於您的建議，使用一個JSON文件管理所有內容：

```json
{
  "metadata": {
    "version": "2.0.0",
    "created_date": "2025-08-01",
    "description": "英語詞彙學習工具 v3 - Stage 2 數據",
    "total_vocabularies": 15,
    "total_assignments": 30
  },

  "vocabularies": [
    // 直接使用參考文件中的15個詞彙，保持原有結構
    {
      "id": 1,
      "text": "festival",
      "phonetic": "/ˈfes.tɪ.vəl/",
      "part_of_speech": "noun",
      "definition_en": "A special day or period, usually in memory of a religious event, with its own social activities, food, or ceremonies.",
      "translation_zh_tw": "節日",
      "sentence_en": "Chinese New Year is the most important festival for Chinese people, celebrated with dragon dances and family gatherings.",
      "image_path": "images/test-16-9.svg",
      "grade": "S1"
    }
    // ... 其餘14個詞彙
  ],

  "student_vocabulary_assignments": [
    {
      "student_username": "student01",
      "student_grade": "S1",
      "assigned_vocabularies": [
        {
          "vocabulary_id": 1,
          "assigned_by": "teacher01",
          "assigned_date": "2025-08-01",
          "status": "assigned",        // assigned, learning, mastered, review
          "progress": 0,               // 0-100
          "practice_count": 0,
          "correct_count": 0,
          "last_practiced": null
        }
        // ... 更多分配
      ]
    }
    // ... 更多學生
  ]
}
```

### 圖片處理策略

#### 階段性圖片方案
- **Stage 2**: 使用佔位圖片，專注功能實現
- **後續階段**: 準備真實圖片

#### 佔位圖片實現
```javascript
// 動態生成佔位圖片URL
function getImageUrl(vocabulary) {
  if (vocabulary.image_path && vocabulary.image_path !== "") {
    return `/uploads/${vocabulary.image_path}`;
  } else {
    // 使用佔位圖片
    return `https://via.placeholder.com/300x200/4F46E5/FFFFFF?text=${encodeURIComponent(vocabulary.text)}`;
  }
}
```

## 🔧 API設計規格

### 基於現有架構的API擴展
Stage 1已完成：
- ✅ `/api/auth/login` - 用戶登入
- ✅ `/health` - 健康檢查
- ✅ JWT認證中間件

### Stage 2.2 詞彙列表管理API (新增)

#### 學生端詞彙列表API (`backend/routes/students.js`)

##### 1. 獲取學生的詞彙列表
```javascript
GET /api/student/vocabulary-lists
Headers: Authorization: Bearer {jwt_token}

Response: {
  "success": true,
  "data": {
    "lists": [
      {
        "id": 1,
        "name": "中一上學期詞彙",
        "description": "中一上學期必學詞彙",
        "grade": "S1",
        "subject": "基礎詞彙",
        "total_words": 15,
        "progress_percentage": 60,
        "due_date": "2025-09-01T00:00:00Z",
        "assigned_at": "2025-08-01T10:00:00Z",
        "status": "active"
      }
    ],
    "statistics": {
      "total_lists": 2,
      "active_lists": 2,
      "completed_lists": 0,
      "overall_progress": 42.5
    }
  }
}
```

##### 2. 獲取特定列表的詞彙
```javascript
GET /api/student/vocabulary-lists/:listId/vocabularies
Headers: Authorization: Bearer {jwt_token}

Response: {
  "success": true,
  "data": {
    "list_info": {
      "id": 1,
      "name": "中一上學期詞彙",
      "description": "中一上學期必學詞彙",
      "total_words": 15,
      "progress_percentage": 60
    },
    "vocabularies": [
      {
        "id": 1,
        "text": "festival",
        "phonetic": "/ˈfes.tɪ.vəl/",
        "part_of_speech": "noun",
        "definition_en": "A special day or period...",
        "translation_zh_tw": "節日",
        "sentence_en": "Chinese New Year is...",
        "image_path": "images/festival.png",
        "order_index": 1,
        "status": "learning",
        "progress": 60,
        "last_practiced": "2024-01-15T10:30:00Z"
      }
    ],
    "statistics": {
      "assigned": 5,
      "learning": 7,
      "mastered": 3,
      "review": 0
    }
  }
}
```

#### 教師端詞彙列表管理API (`backend/routes/teacher-vocabulary-lists.js`)

##### 1. 獲取教師創建的詞彙列表
```javascript
GET /api/teacher/vocabulary-lists
Headers: Authorization: Bearer {jwt_token}

Response: {
  "success": true,
  "data": {
    "lists": [
      {
        "id": 1,
        "name": "中一上學期詞彙",
        "description": "中一上學期必學詞彙",
        "grade": "S1",
        "subject": "基礎詞彙",
        "total_words": 15,
        "assigned_students_count": 8,
        "created_at": "2025-08-01T10:00:00Z",
        "updated_at": "2025-08-01T15:30:00Z"
      }
    ]
  }
}
```

##### 2. 創建新的詞彙列表
```javascript
POST /api/teacher/vocabulary-lists
Headers: Authorization: Bearer {jwt_token}
Content-Type: application/json

Body: {
  "name": "節日主題詞彙",
  "description": "關於節日的詞彙學習",
  "grade": "S1",
  "subject": "節日",
  "vocabulary_ids": [1, 2, 3, 4, 5]
}

Response: {
  "success": true,
  "data": {
    "list_id": 2,
    "message": "詞彙列表創建成功"
  }
}
```

##### 3. ZIP文件上傳創建詞彙列表
```javascript
POST /api/teacher/vocabulary-lists/upload
Headers: Authorization: Bearer {jwt_token}
Content-Type: multipart/form-data

Body: {
  "zipFile": [ZIP文件],
  "name": "新詞彙列表",
  "description": "描述",
  "grade": "S1",
  "subject": "主題"
}

Response: {
  "success": true,
  "data": {
    "list_id": 3,
    "vocabularies_created": 10,
    "images_uploaded": 10,
    "message": "詞彙列表上傳成功"
  }
}
```

##### 4. 分配詞彙列表給學生
```javascript
POST /api/teacher/vocabulary-lists/:listId/assign
Headers: Authorization: Bearer {jwt_token}
Content-Type: application/json

Body: {
  "student_ids": [1, 2, 3],
  "due_date": "2025-09-01T00:00:00Z"
}

Response: {
  "success": true,
  "data": {
    "assigned_count": 3,
    "message": "詞彙列表分配成功"
  }
}
```

### Stage 2.1 現有API (已完成)

#### 學生端API (`backend/routes/students.js`)

##### 1. 獲取學生詞彙列表
```javascript
GET /api/student/vocabularies
Headers: Authorization: Bearer {jwt_token}

Response: {
  "success": true,
  "data": {
    "vocabularies": [
      {
        "id": 1,
        "text": "festival",
        "phonetic": "/ˈfes.tɪ.vəl/",
        "part_of_speech": "noun",
        "definition_en": "A special day or period...",
        "translation_zh_tw": "節日",
        "sentence_en": "Chinese New Year is the most important festival...",
        "image_path": "images/test-16-9.svg",
        "grade": "S1",
        // 學習狀態信息
        "status": "assigned",      // assigned, learning, mastered, review
        "progress": 0,             // 0-100
        "practice_count": 0,
        "correct_count": 0,
        "last_practiced": null
      }
    ],
    "total": 5,
    "stats": {
      "assigned": 3,
      "learning": 1,
      "mastered": 1,
      "review": 0
    }
  }
}
```

##### 2. 獲取特定詞彙詳情
```javascript
GET /api/student/vocabulary/:id
Response: {
  "success": true,
  "data": {
    "vocabulary": {
      "id": 1,
      "text": "festival",
      "phonetic": "/ˈfes.tɪ.vəl/",
      "part_of_speech": "noun",
      "definition_en": "A special day or period...",
      "translation_zh_tw": "節日",
      "sentence_en": "Chinese New Year is the most important festival...",
      "image_path": "images/test-16-9.svg",
      "grade": "S1"
    },
    "user_progress": {
      "status": "learning",
      "progress": 60,
      "practice_count": 8,
      "correct_count": 6,
      "last_practiced": "2025-08-01T10:30:00Z"
    }
  }
}
```

#### 練習系統API (`backend/routes/practice.js`)

##### 1. 獲取練習題目
```javascript
GET /api/practice/questions?type=multiple_choice&count=5
Headers: Authorization: Bearer {jwt_token}

Response: {
  "success": true,
  "data": {
    "practice_session_id": "session_123",
    "questions": [
      {
        "id": 1,
        "vocabulary_id": 1,
        "type": "multiple_choice",
        "question": "「festival」的中文意思是？",
        "options": ["節日", "假期", "慶典", "活動"],
        "correct_answer": "節日",
        "word_info": {
          "text": "festival",
          "phonetic": "/ˈfes.tɪ.vəl/"
        }
      }
    ],
    "time_limit": 300
  }
}
```

##### 2. 提交練習結果
```javascript
POST /api/practice/submit
Body: {
  "practice_session_id": "session_123",
  "answers": [
    {
      "question_id": 1,
      "vocabulary_id": 1,
      "user_answer": "節日",
      "is_correct": true,
      "time_spent": 15
    }
  ]
}

Response: {
  "success": true,
  "data": {
    "session_results": {
      "total_questions": 5,
      "correct_answers": 4,
      "accuracy": 80,
      "total_time": 120,
      "score": 80
    },
    "vocabulary_updates": [
      {
        "vocabulary_id": 1,
        "new_status": "learning",
        "progress_change": +10,
        "new_progress": 70
      }
    ]
  }
}
```

##### 3. 獲取學習統計
```javascript
GET /api/student/statistics
Response: {
  "success": true,
  "data": {
    "overall_stats": {
      "total_vocabularies": 5,
      "mastered_count": 1,
      "learning_count": 2,
      "assigned_count": 2,
      "overall_accuracy": 75
    },
    "recent_activity": [
      {
        "date": "2025-08-01",
        "practices": 3,
        "accuracy": 80
      }
    ]
  }
}
```

## 🎮 練習系統設計

### 基於現有詞彙的練習類型

#### 1. 多選題 (Multiple Choice)
基於參考文件中的15個詞彙生成題目：

```javascript
// 題目生成邏輯
function generateMultipleChoice(targetVocab, allVocabs) {
  const correctAnswer = targetVocab.translation_zh_tw;

  // 從其他詞彙中選擇3個錯誤答案
  const wrongAnswers = allVocabs
    .filter(v => v.id !== targetVocab.id && v.translation_zh_tw !== correctAnswer)
    .map(v => v.translation_zh_tw)
    .sort(() => 0.5 - Math.random())
    .slice(0, 3);

  const options = shuffleArray([correctAnswer, ...wrongAnswers]);

  return {
    type: 'multiple_choice',
    question: `「${targetVocab.text}」的中文意思是？`,
    options: options,
    correct_answer: correctAnswer,
    explanation: targetVocab.definition_en
  };
}
```

#### 2. 填空題 (Fill in the Blank)
```javascript
function generateFillBlank(targetVocab) {
  const sentence = targetVocab.sentence_en;
  const wordToHide = targetVocab.text;

  // 將目標單詞替換為空白
  const blankedSentence = sentence.replace(
    new RegExp(`\\b${wordToHide}\\b`, 'gi'),
    '______'
  );

  return {
    type: 'fill_blank',
    question: `請填入正確的英文單詞：${blankedSentence}`,
    correct_answer: wordToHide.toLowerCase(),
    hint: `中文意思：${targetVocab.translation_zh_tw}`,
    phonetic: targetVocab.phonetic
  };
}
```

#### 3. 拼寫練習 (Typing)
```javascript
function generateTyping(targetVocab) {
  return {
    type: 'typing',
    question: `請拼寫「${targetVocab.translation_zh_tw}」的英文：`,
    correct_answer: targetVocab.text.toLowerCase(),
    phonetic: targetVocab.phonetic,
    hint: `音標：${targetVocab.phonetic}`,
    definition: targetVocab.definition_en
  };
}
```

### 進度計算邏輯

#### 基於練習結果的進度更新
```javascript
function updateVocabularyProgress(currentProgress, isCorrect, practiceType) {
  let progressChange = 0;

  if (isCorrect) {
    // 根據練習類型給予不同分數
    const typeMultiplier = {
      'multiple_choice': 1.0,    // 基礎分數
      'fill_blank': 1.2,         // 填空題較難，多給20%
      'typing': 1.5              // 拼寫最難，多給50%
    };

    progressChange = 10 * (typeMultiplier[practiceType] || 1.0);
  } else {
    // 錯誤時減少進度，但不低於0
    progressChange = -5;
  }

  const newProgress = Math.max(0, Math.min(100, currentProgress + progressChange));

  // 狀態更新邏輯
  let newStatus = 'assigned';
  if (newProgress >= 80) {
    newStatus = 'mastered';
  } else if (newProgress >= 30) {
    newStatus = 'learning';
  } else if (newProgress < 30 && currentProgress >= 30) {
    newStatus = 'review';
  }

  return { newProgress, newStatus, progressChange };
}
```

## 🔄 前端整合策略

### 基於現有StudyView.vue的修改

#### 當前狀況分析
現有的 `frontend/src/views/student/StudyView.vue` 已經有：
- ✅ 完整的UI組件結構
- ✅ 響應式設計
- ✅ 模擬數據載入邏輯（第158-202行）
- ✅ 語音播放功能
- ✅ 詞彙卡片切換邏輯

#### 需要修改的重點

##### 1. 替換模擬數據載入
```javascript
// 修改 loadVocabularies() 函數
async function loadVocabularies() {
  isLoading.value = true;
  error.value = null;

  try {
    const response = await fetch('/api/student/vocabularies', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: 載入詞彙失敗`);
    }

    const result = await response.json();
    vocabularies.value = result.data.vocabularies;

    // 更新統計信息
    stats.value = result.data.stats;

  } catch (err) {
    console.error('載入詞彙失敗:', err);
    error.value = '載入詞彙失敗，請重試';
  } finally {
    isLoading.value = false;
  }
}
```

##### 2. 圖片URL處理邏輯
```javascript
// 計算屬性：動態處理圖片URL
const currentWordImageUrl = computed(() => {
  if (!currentWord.value) return null;

  if (currentWord.value.image_path && currentWord.value.image_path !== "") {
    return `/uploads/${currentWord.value.image_path}`;
  } else {
    // 使用佔位圖片
    const word = encodeURIComponent(currentWord.value.text);
    return `https://via.placeholder.com/300x200/4F46E5/FFFFFF?text=${word}`;
  }
});
```

### PracticeView.vue 完整重新實現

#### 當前狀況
現有的 `PracticeView.vue` 只是佔位頁面，需要完全重新實現。

#### 新的組件架構
```vue
<template>
  <div class="p-6">
    <!-- 練習標題 -->
    <div class="mb-6">
      <h2 class="text-2xl font-bold text-slate-900">練習測驗</h2>
      <p class="text-slate-600">通過練習鞏固您的詞彙知識</p>
    </div>

    <!-- 練習選擇 -->
    <div v-if="!practiceStarted" class="space-y-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button
          @click="startPractice('multiple_choice')"
          class="practice-type-btn"
        >
          <h3>多選題練習</h3>
          <p>選擇正確的中文意思</p>
        </button>

        <button
          @click="startPractice('fill_blank')"
          class="practice-type-btn"
        >
          <h3>填空題練習</h3>
          <p>在句子中填入正確單詞</p>
        </button>

        <button
          @click="startPractice('typing')"
          class="practice-type-btn"
        >
          <h3>拼寫練習</h3>
          <p>根據中文意思拼寫英文</p>
        </button>
      </div>
    </div>

    <!-- 練習進行中 -->
    <div v-else-if="currentQuestion" class="practice-session">
      <!-- 進度條 -->
      <div class="progress-bar mb-6">
        <div class="progress" :style="{width: `${progressPercentage}%`}"></div>
        <span class="progress-text">{{ currentQuestionIndex + 1 }} / {{ totalQuestions }}</span>
      </div>

      <!-- 題目組件 -->
      <component
        :is="currentQuestionComponent"
        :question="currentQuestion"
        @answer="handleAnswer"
      />
    </div>

    <!-- 練習結果 -->
    <div v-else-if="practiceCompleted" class="results">
      <div class="text-center">
        <h3 class="text-2xl font-bold mb-4">練習完成！</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-value">{{ results.correct_answers }}</span>
            <span class="stat-label">正確答案</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ results.accuracy }}%</span>
            <span class="stat-label">正確率</span>
          </div>
        </div>
        <button @click="resetPractice" class="btn-primary mt-6">
          再次練習
        </button>
      </div>
    </div>
  </div>
</template>
```

#### 練習組件設計
```javascript
// components/practice/MultipleChoiceQuestion.vue
// components/practice/FillBlankQuestion.vue
// components/practice/TypingQuestion.vue
```

## 📝 Stage 2 開發步驟詳解

### 第1步：數據準備和腳本開發 (1天)

#### 1.1 創建統一數據管理腳本
```bash
# 創建數據腳本
touch backend/scripts/createVocabulariesAndAssignments.js
touch backend/scripts/stage2-data.json
```

#### 1.2 準備完整數據文件
基於參考文件的15個詞彙 + 學生分配關係：
- **詞彙數據**: 直接使用 `Reference/Sample for refernce/frontend/data/mockData.json` 的15個詞彙
- **分配策略**: 為10個學生（student01-student10）分配不同的詞彙組合
- **學習狀態**: 初始化不同的學習進度狀態

#### 1.3 執行數據創建和驗證
```bash
cd backend/scripts
node createVocabulariesAndAssignments.js stage2-data.json
# 驗證數據庫內容
node -e "const db = require('../config/database'); db.all('SELECT COUNT(*) as count FROM vocabularies').then(console.log);"
```

### 第2步：後端API開發 (2天)

#### 2.1 學生端API實現
```bash
# 創建或修改學生路由
# backend/routes/students.js 可能已存在，需要擴展
```

**實現的API端點**:
- `GET /api/student/vocabularies` - 獲取學生詞彙列表
- `GET /api/student/vocabulary/:id` - 獲取詞彙詳情
- `GET /api/student/statistics` - 獲取學習統計

#### 2.2 練習系統API實現
```bash
touch backend/routes/practice.js
```

**實現的API端點**:
- `GET /api/practice/questions` - 獲取練習題目
- `POST /api/practice/submit` - 提交練習結果
- `GET /api/practice/history` - 獲取練習歷史

#### 2.3 整合到主服務器
修改 `backend/server.js`，添加新的路由：
```javascript
app.use('/api/student', require('./routes/students'));
app.use('/api/practice', require('./routes/practice'));
```

### 第3步：前端功能實現 (2天)

#### 3.1 修改StudyView.vue
**目標**: 將模擬數據替換為真實API調用
- 修改 `loadVocabularies()` 函數（第158-202行）
- 添加錯誤處理和載入狀態
- 實現圖片URL動態處理
- 測試響應式設計

#### 3.2 重新實現PracticeView.vue
**目標**: 從佔位頁面變成完整的練習系統
- 創建練習類型選擇界面
- 實現3種練習組件（多選題、填空題、拼寫練習）
- 添加練習進度和結果顯示
- 整合API調用和狀態管理

#### 3.3 創建練習子組件
```bash
mkdir frontend/src/components/practice
touch frontend/src/components/practice/MultipleChoiceQuestion.vue
touch frontend/src/components/practice/FillBlankQuestion.vue
touch frontend/src/components/practice/TypingQuestion.vue
```

### 第4步：整合測試和優化 (1天)

#### 4.1 功能完整性測試
- **詞彙學習流程**: 登入 → 查看詞彙 → 學習卡片 → 語音播放
- **練習系統流程**: 選擇練習類型 → 答題 → 查看結果 → 進度更新
- **數據一致性**: 練習結果正確記錄到數據庫
- **響應式設計**: 手機、平板、桌面端都正常工作

#### 4.2 用戶體驗優化
- 載入動畫和狀態反饋
- 錯誤信息友好顯示
- 練習結果即時反饋
- 性能優化（API響應時間、圖片載入）

## 🧪 測試策略

### 基於Stage 1測試基礎的擴展測試

#### API端點測試
```powershell
# 測試學生詞彙列表API（需要先登入獲取token）
$loginBody = @{username='student01'; password='student123'} | ConvertTo-Json
$loginResponse = Invoke-WebRequest -Uri 'http://localhost:3000/api/auth/login' -Method POST -Body $loginBody -ContentType 'application/json'
$token = ($loginResponse.Content | ConvertFrom-Json).token

# 測試詞彙列表
Invoke-WebRequest -Uri 'http://localhost:3000/api/student/vocabularies' -Headers @{Authorization="Bearer $token"}

# 測試練習題目獲取
Invoke-WebRequest -Uri 'http://localhost:3000/api/practice/questions?type=multiple_choice&count=5' -Headers @{Authorization="Bearer $token"}
```

#### 前端功能測試
**使用現有測試帳號**:
- **學生帳號**: student01 / student123
- **教師帳號**: teacher01 / teacher123

**測試流程**:
1. 登入學生帳號
2. 導航到"詞彙學習"頁面
3. 驗證詞彙卡片顯示真實數據
4. 測試語音播放功能
5. 導航到"練習測驗"頁面
6. 測試3種練習類型
7. 驗證練習結果和進度更新

## 🎯 Stage 2 成功標準

### 核心功能標準（必須達成）
- [x] **詞彙學習系統** ✅ 已完成
  - 學生可以看到分配的詞彙列表（基於15個參考詞彙）
  - 詞彙卡片顯示完整信息（文字、音標、定義、例句、圖片）
  - 語音播放功能正常工作
  - 圖片顯示（佔位圖片或真實圖片）

- [x] **練習系統** ✅ 已完成
  - 3種練習類型可用（多選題、填空題、拼寫練習）
  - 練習結果正確記錄到數據庫
  - 學習進度正確更新
  - 練習統計準確顯示

- [x] **數據完整性** ✅ 已完成
  - 15個詞彙正確載入到數據庫
  - 學生詞彙分配關係正確建立
  - 練習記錄正確保存
  - 進度計算邏輯正確

### 技術標準
- [x] API響應時間 < 2秒 ✅ 已完成
- [x] 前端載入流暢，無明顯延遲 ✅ 已完成
- [x] 錯誤處理完善，用戶友好 ✅ 已完成
- [x] 響應式設計在手機/平板/桌面都正常 ✅ 已完成
- [x] 數據庫查詢優化，無性能問題 ✅ 已完成

### 用戶體驗標準
- [x] 界面直觀易用，符合Stage 1的設計風格 ✅ 已完成
- [x] 反饋及時準確，載入狀態清晰 ✅ 已完成
- [x] 錯誤信息友好，不顯示技術細節 ✅ 已完成
- [x] 練習流程順暢，結果反饋即時 ✅ 已完成

## 📚 開發參考資料

### 現有代碼基礎（Stage 1已完成）
- `frontend/src/views/student/StudyView.vue` - 詞彙卡片組件基礎
- `frontend/src/views/student/PracticeView.vue` - 練習頁面佔位
- `backend/scripts/createUsers.js` - 數據創建腳本參考
- `backend/routes/auth.js` - API路由實現參考
- `backend/config/database.js` - 數據庫操作方法

### 參考數據結構
- `Reference/Sample for refernce/frontend/data/mockData.json` - 15個詞彙數據
- `backend/database/init.sql` - 數據庫表結構
- `Stage1/TESTING_CHECKLIST.md` - 測試方法參考

### 技術文檔
- Vue.js 3 Composition API 官方文檔
- Express.js 路由和中間件指南
- SQLite 查詢優化最佳實踐
- TailwindCSS 響應式設計

### 設計一致性參考
- Stage1/RESPONSIVE_DESIGN_IMPLEMENTATION.md - 響應式設計規範
- 現有的登入頁面和儀表板設計風格
- TailwindCSS 配色方案和組件樣式

## 📋 開發完成檢查清單

### 數據準備
- [x] 創建 `backend/scripts/createVocabulariesAndAssignments.js` ✅ 已完成
- [x] 創建 `backend/scripts/stage2-data.json` ✅ 已完成
- [x] 執行腳本，驗證數據庫內容 ✅ 已完成

### 後端API
- [x] 實現 `GET /api/student/vocabularies` ✅ 已完成
- [x] 實現 `GET /api/student/vocabulary/:id` ✅ 已完成
- [x] 實現 `GET /api/practice/questions` ✅ 已完成
- [x] 實現 `POST /api/practice/submit` ✅ 已完成
- [x] 實現 `GET /api/student/statistics` ✅ 已完成

### 前端功能
- [x] 修改 StudyView.vue 的數據載入邏輯 ✅ 已完成
- [x] 重新實現 PracticeView.vue ✅ 已完成
- [x] 創建練習子組件 ✅ 已完成
- [x] 整合API調用和錯誤處理 ✅ 已完成

### 測試驗證
- [x] API端點測試通過 ✅ 已完成
- [x] 前端功能測試通過 ✅ 已完成
- [x] 響應式設計測試通過 ✅ 已完成
- [x] 數據一致性測試通過 ✅ 已完成

### 詞彙列表管理系統 (Stage 2.2)
- [x] 數據庫架構升級 ✅ 已完成
- [x] 學生界面重構 ✅ 已完成
- [x] 教師管理界面 ✅ 已完成
- [x] ZIP文件上傳功能 ✅ 已完成
- [x] 學生分配管理 ✅ 已完成

---

**文檔版本**: 2.0.0
**創建日期**: 2025-08-01
**基於**: Reference/DEVELOPMENT_PLAN.md, Reference/PROJECT_SPECIFICATION.md, Stage1完成狀況
**適用階段**: Stage 2 - 學生端核心功能開發
**預估完成時間**: 6天
**下一階段**: Stage 2.5 - 教師端基礎功能（週5-6）
