# 詞彙列表管理系統實施路線圖

## 📋 總體規劃

### 實施順序
```
階段1: 數據庫架構升級 (1天)
   ↓
階段2: 學生端界面重構 (2天)
   ↓
階段3: 教師端管理界面 (2天)
   ↓
階段4: 整合測試優化 (1天)
```

### 預期成果
- ✅ 完整的詞彙列表管理系統
- ✅ 學生可選擇詞彙列表學習
- ✅ 教師可通過Web界面管理詞彙
- ✅ 支持ZIP文件上傳功能
- ✅ 替代命令行數據管理方式

## 🗄️ 階段1：數據庫架構升級

### 1.1 創建數據庫遷移腳本
**文件**: `backend/database/migrations/add_vocabulary_lists.sql`

```sql
-- 詞彙列表主表
CREATE TABLE vocabulary_lists (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    grade TEXT,
    subject TEXT,
    total_words INTEGER DEFAULT 0,
    created_by INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 詞彙列表內容表
CREATE TABLE vocabulary_list_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    list_id INTEGER NOT NULL,
    vocabulary_id INTEGER NOT NULL,
    order_index INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (list_id) REFERENCES vocabulary_lists(id) ON DELETE CASCADE,
    FOREIGN KEY (vocabulary_id) REFERENCES vocabularies(id) ON DELETE CASCADE,
    UNIQUE(list_id, vocabulary_id)
);

-- 學生詞彙列表分配表
CREATE TABLE user_vocabulary_lists (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    list_id INTEGER NOT NULL,
    assigned_by INTEGER NOT NULL,
    assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    due_date DATETIME,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'archived')),
    progress_percentage INTEGER DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (list_id) REFERENCES vocabulary_lists(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id),
    UNIQUE(user_id, list_id)
);

-- 創建索引
CREATE INDEX idx_vocabulary_lists_created_by ON vocabulary_lists(created_by);
CREATE INDEX idx_vocabulary_lists_grade ON vocabulary_lists(grade);
CREATE INDEX idx_vocabulary_list_items_list_id ON vocabulary_list_items(list_id);
CREATE INDEX idx_user_vocabulary_lists_user_id ON user_vocabulary_lists(user_id);
```

### 1.2 創建數據遷移腳本
**文件**: `backend/scripts/migrateToVocabularyLists.js`

```javascript
const database = require('../config/database');

async function migrateExistingData() {
    try {
        console.log('開始數據遷移...');
        
        // 1. 執行數據庫架構更新
        const migrationSQL = fs.readFileSync('./database/migrations/add_vocabulary_lists.sql', 'utf8');
        await database.exec(migrationSQL);
        
        // 2. 創建默認詞彙列表
        const defaultLists = [
            {
                name: '中一基礎詞彙',
                description: '中一學生基礎詞彙學習',
                grade: 'S1',
                subject: '基礎詞彙',
                created_by: 1 // teacher01
            },
            {
                name: '中二進階詞彙',
                description: '中二學生進階詞彙學習',
                grade: 'S2',
                subject: '進階詞彙',
                created_by: 1
            }
        ];
        
        // 3. 遷移現有詞彙到列表中
        for (const listData of defaultLists) {
            const listResult = await database.run(`
                INSERT INTO vocabulary_lists (name, description, grade, subject, created_by)
                VALUES (?, ?, ?, ?, ?)
            `, [listData.name, listData.description, listData.grade, listData.subject, listData.created_by]);
            
            const listId = listResult.lastID;
            
            // 獲取對應年級的詞彙
            const vocabularies = await database.all(`
                SELECT DISTINCT v.id 
                FROM vocabularies v
                INNER JOIN user_vocabularies uv ON v.id = uv.vocabulary_id
                INNER JOIN users u ON uv.user_id = u.id
                WHERE u.grade = ?
            `, [listData.grade]);
            
            // 添加詞彙到列表
            for (let i = 0; i < vocabularies.length; i++) {
                await database.run(`
                    INSERT INTO vocabulary_list_items (list_id, vocabulary_id, order_index)
                    VALUES (?, ?, ?)
                `, [listId, vocabularies[i].id, i + 1]);
            }
            
            // 更新詞彙總數
            await database.run(`
                UPDATE vocabulary_lists 
                SET total_words = ? 
                WHERE id = ?
            `, [vocabularies.length, listId]);
            
            // 為學生分配列表
            const students = await database.all(`
                SELECT id FROM users WHERE role = 'student' AND grade = ?
            `, [listData.grade]);
            
            for (const student of students) {
                await database.run(`
                    INSERT INTO user_vocabulary_lists (user_id, list_id, assigned_by)
                    VALUES (?, ?, ?)
                `, [student.id, listId, listData.created_by]);
            }
        }
        
        console.log('數據遷移完成！');
        
    } catch (error) {
        console.error('數據遷移失敗:', error);
        throw error;
    }
}

if (require.main === module) {
    migrateExistingData()
        .then(() => process.exit(0))
        .catch(() => process.exit(1));
}

module.exports = { migrateExistingData };
```

### 1.3 驗證腳本
**文件**: `backend/scripts/verifyMigration.js`

```javascript
const database = require('../config/database');

async function verifyMigration() {
    try {
        // 檢查新表是否創建成功
        const tables = await database.all(`
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name LIKE 'vocabulary_%'
        `);
        
        console.log('數據庫表:', tables.map(t => t.name));
        
        // 檢查數據遷移結果
        const listsCount = await database.get('SELECT COUNT(*) as count FROM vocabulary_lists');
        const itemsCount = await database.get('SELECT COUNT(*) as count FROM vocabulary_list_items');
        const assignmentsCount = await database.get('SELECT COUNT(*) as count FROM user_vocabulary_lists');
        
        console.log(`詞彙列表數量: ${listsCount.count}`);
        console.log(`列表項目數量: ${itemsCount.count}`);
        console.log(`學生分配數量: ${assignmentsCount.count}`);
        
        // 檢查數據完整性
        const integrityCheck = await database.all(`
            SELECT 
                vl.name,
                vl.total_words,
                COUNT(vli.id) as actual_words,
                COUNT(uvl.id) as assigned_students
            FROM vocabulary_lists vl
            LEFT JOIN vocabulary_list_items vli ON vl.id = vli.list_id
            LEFT JOIN user_vocabulary_lists uvl ON vl.id = uvl.list_id
            GROUP BY vl.id, vl.name, vl.total_words
        `);
        
        console.log('數據完整性檢查:');
        console.table(integrityCheck);
        
    } catch (error) {
        console.error('驗證失敗:', error);
    }
}

if (require.main === module) {
    verifyMigration();
}
```

## 🎯 階段2：學生端界面重構

### 2.1 創建詞彙列表選擇頁面
**文件**: `frontend/src/views/student/VocabularyListsView.vue`

### 2.2 修改現有學習頁面
**文件**: `frontend/src/views/student/StudyView.vue`
- 添加列表上下文信息
- 修改API調用邏輯
- 添加返回列表選擇功能

### 2.3 更新路由配置
**文件**: `frontend/src/router/index.js`
- 添加詞彙列表選擇路由
- 修改學習頁面路由參數

### 2.4 更新導航菜單
**文件**: `frontend/src/views/student/StudentLayout.vue`
- 修改"詞彙學習"菜單項指向列表選擇頁面

## 🎓 階段3：教師端管理界面

### 3.1 創建詞彙列表管理頁面
**文件**: `frontend/src/views/teacher/VocabularyListsManagementView.vue`

### 3.2 實現ZIP文件上傳功能
**文件**: `frontend/src/components/teacher/VocabularyListUpload.vue`

### 3.3 創建學生分配管理組件
**文件**: `frontend/src/components/teacher/StudentAssignmentModal.vue`

### 3.4 後端API實現
**文件**: `backend/routes/teacher-vocabulary-lists.js`
- 列表CRUD操作
- ZIP文件處理
- 學生分配管理

## 🧪 階段4：整合測試優化

### 4.1 功能測試清單
- [ ] 學生可以看到分配的詞彙列表
- [ ] 學生可以選擇列表進行學習
- [ ] 學習進度正確更新到列表級別
- [ ] 教師可以創建新的詞彙列表
- [ ] ZIP文件上傳和解析正常工作
- [ ] 學生分配功能正常
- [ ] 所有API端點響應正確

### 4.2 性能測試
- [ ] 大量詞彙列表載入性能
- [ ] ZIP文件上傳處理時間
- [ ] 數據庫查詢優化效果
- [ ] 前端頁面載入速度

### 4.3 用戶體驗測試
- [ ] 學習流程直觀易懂
- [ ] 管理操作簡單高效
- [ ] 錯誤處理友好
- [ ] 響應式設計正常

## 📝 開發檢查清單

### 數據庫階段
- [ ] 創建遷移腳本
- [ ] 執行數據遷移
- [ ] 驗證數據完整性
- [ ] 測試查詢性能

### 學生端階段
- [ ] 創建列表選擇頁面
- [ ] 修改學習頁面
- [ ] 更新路由配置
- [ ] 測試學習流程

### 教師端階段
- [ ] 創建管理頁面
- [ ] 實現上傳功能
- [ ] 添加分配功能
- [ ] 測試管理流程

### 整合測試階段
- [ ] 端到端功能測試
- [ ] 性能基準測試
- [ ] 用戶體驗測試
- [ ] 文檔更新完善

## 🎯 成功標準

### 核心功能
- ✅ 詞彙列表管理系統完全替代現有單詞直接分配方式
- ✅ 學生學習流程：選擇列表 → 學習詞彙 → 練習測驗
- ✅ 教師管理流程：創建列表 → 上傳內容 → 分配學生
- ✅ ZIP文件上傳功能完全替代命令行數據管理

### 技術指標
- ✅ API響應時間 < 2秒
- ✅ 文件上傳處理時間 < 30秒
- ✅ 數據庫查詢優化，支持100+詞彙列表
- ✅ 前端界面響應式，支持所有設備

### 用戶體驗
- ✅ 學習流程比現有方式更直觀
- ✅ 教師管理比命令行方式更簡單
- ✅ 錯誤處理完善，用戶友好
- ✅ 整體設計風格與現有系統一致

---

**文檔版本**: 1.0.0  
**創建日期**: 2025-08-02  
**預估完成時間**: 6天  
**負責開發**: AI助手 + 用戶協作
