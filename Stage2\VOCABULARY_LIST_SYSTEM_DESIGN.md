# 詞彙列表管理系統設計文檔

## 📋 系統概述

### 設計目標
解決當前系統的架構問題，實現真正的詞彙列表管理功能：
1. **教師可以創建主題化的詞彙列表**（如：節日主題、中一上學期詞彙）
2. **學生可以選擇要學習的詞彙列表**，而不是直接進入詞彙卡片
3. **支持ZIP文件上傳**，包含圖片和JSON數據
4. **替代命令行數據管理**，提供完整的Web界面

### 核心概念
```
詞彙列表 (Vocabulary List)
├── 基本信息：名稱、描述、年級、主題
├── 詞彙內容：包含多個詞彙，有順序
├── 分配管理：可分配給多個學生
└── 進度追蹤：整體完成度統計
```

## 🗄️ 數據庫架構

### 新增表結構

#### 1. vocabulary_lists (詞彙列表主表)
```sql
CREATE TABLE vocabulary_lists (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,                    -- "中一上學期詞彙"、"節日主題詞彙"
    description TEXT,                      -- 詳細描述
    grade TEXT,                           -- S1, S2, S3
    subject TEXT,                         -- 節日、食物、運動等主題
    total_words INTEGER DEFAULT 0,        -- 詞彙總數
    created_by INTEGER NOT NULL,          -- 創建教師ID
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

#### 2. vocabulary_list_items (詞彙列表內容表)
```sql
CREATE TABLE vocabulary_list_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    list_id INTEGER NOT NULL,
    vocabulary_id INTEGER NOT NULL,
    order_index INTEGER DEFAULT 0,        -- 學習順序
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (list_id) REFERENCES vocabulary_lists(id) ON DELETE CASCADE,
    FOREIGN KEY (vocabulary_id) REFERENCES vocabularies(id) ON DELETE CASCADE,
    UNIQUE(list_id, vocabulary_id)
);
```

#### 3. user_vocabulary_lists (學生詞彙列表分配表)
```sql
CREATE TABLE user_vocabulary_lists (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    list_id INTEGER NOT NULL,
    assigned_by INTEGER NOT NULL,         -- 分配教師ID
    assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    due_date DATETIME,                    -- 完成期限
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'archived')),
    progress_percentage INTEGER DEFAULT 0, -- 整體完成百分比
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (list_id) REFERENCES vocabulary_lists(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id),
    UNIQUE(user_id, list_id)
);
```

### 索引優化
```sql
-- 提升查詢性能的索引
CREATE INDEX idx_vocabulary_lists_created_by ON vocabulary_lists(created_by);
CREATE INDEX idx_vocabulary_lists_grade ON vocabulary_lists(grade);
CREATE INDEX idx_vocabulary_list_items_list_id ON vocabulary_list_items(list_id);
CREATE INDEX idx_vocabulary_list_items_order ON vocabulary_list_items(list_id, order_index);
CREATE INDEX idx_user_vocabulary_lists_user_id ON user_vocabulary_lists(user_id);
CREATE INDEX idx_user_vocabulary_lists_status ON user_vocabulary_lists(user_id, status);
```

## 🎯 學生端界面重構

### 新的學習流程設計

#### 1. 詞彙列表選擇頁面 (VocabularyListsView.vue)
```vue
<template>
  <div class="vocabulary-lists-page">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h2>選擇詞彙列表</h2>
      <p>選擇您要學習的詞彙主題</p>
    </div>

    <!-- 詞彙列表卡片 -->
    <div class="lists-grid">
      <div 
        v-for="list in vocabularyLists" 
        :key="list.id"
        class="list-card"
        @click="selectList(list.id)"
      >
        <!-- 列表基本信息 -->
        <div class="list-info">
          <h3>{{ list.name }}</h3>
          <p>{{ list.description }}</p>
          <div class="list-meta">
            <span class="grade-badge">{{ list.grade }}</span>
            <span class="subject-badge">{{ list.subject }}</span>
            <span class="word-count">{{ list.total_words }} 個詞彙</span>
          </div>
        </div>

        <!-- 學習進度 -->
        <div class="progress-section">
          <div class="progress-bar">
            <div class="progress-fill" :style="{width: `${list.progress_percentage}%`}"></div>
          </div>
          <span class="progress-text">{{ list.progress_percentage }}% 完成</span>
        </div>

        <!-- 期限信息 -->
        <div v-if="list.due_date" class="due-date">
          <span>期限：{{ formatDate(list.due_date) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
```

#### 2. 修改後的詞彙學習頁面 (StudyView.vue)
```vue
<template>
  <div class="study-page">
    <!-- 列表信息欄 -->
    <div class="list-header">
      <button @click="backToLists" class="back-button">
        ← 返回列表選擇
      </button>
      <div class="current-list-info">
        <h2>{{ currentList.name }}</h2>
        <p>{{ currentList.description }}</p>
        <div class="study-progress">
          <span>進度：{{ currentWordIndex + 1 }} / {{ totalWords }}</span>
          <div class="progress-bar">
            <div class="progress-fill" :style="{width: `${studyProgress}%`}"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 現有的詞彙卡片內容 -->
    <div class="vocabulary-card">
      <!-- 保持現有的詞彙卡片設計 -->
    </div>
  </div>
</template>
```

### 路由配置更新
```javascript
// frontend/src/router/index.js
const studentRoutes = [
  {
    path: '/student',
    component: StudentLayout,
    children: [
      { path: '', component: DashboardView },
      { path: 'vocabulary-lists', component: VocabularyListsView }, // 新增
      { path: 'study/:listId', component: StudyView },              // 修改
      { path: 'practice', component: PracticeView },
      { path: 'progress', component: ProgressView },
      { path: 'review', component: ReviewView }
    ]
  }
]
```

## 🎓 教師端管理界面

### 詞彙列表管理頁面 (TeacherVocabularyListsView.vue)

#### 1. 列表管理主頁面
```vue
<template>
  <div class="teacher-vocabulary-management">
    <!-- 操作工具欄 -->
    <div class="toolbar">
      <h2>詞彙列表管理</h2>
      <div class="actions">
        <button @click="showCreateModal = true" class="btn-primary">
          + 創建新列表
        </button>
        <button @click="showUploadModal = true" class="btn-secondary">
          📁 上傳ZIP文件
        </button>
      </div>
    </div>

    <!-- 列表表格 -->
    <div class="lists-table">
      <table>
        <thead>
          <tr>
            <th>列表名稱</th>
            <th>年級</th>
            <th>主題</th>
            <th>詞彙數量</th>
            <th>分配學生數</th>
            <th>創建時間</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="list in vocabularyLists" :key="list.id">
            <td>{{ list.name }}</td>
            <td>{{ list.grade }}</td>
            <td>{{ list.subject }}</td>
            <td>{{ list.total_words }}</td>
            <td>{{ list.assigned_students_count }}</td>
            <td>{{ formatDate(list.created_at) }}</td>
            <td>
              <button @click="editList(list)" class="btn-edit">編輯</button>
              <button @click="assignList(list)" class="btn-assign">分配</button>
              <button @click="deleteList(list)" class="btn-delete">刪除</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
```

#### 2. ZIP文件上傳功能
```vue
<template>
  <div class="upload-modal" v-if="showUploadModal">
    <div class="modal-content">
      <h3>上傳詞彙列表ZIP文件</h3>
      
      <!-- 文件上傳區域 -->
      <div class="upload-area" @drop="handleDrop" @dragover.prevent>
        <input type="file" @change="handleFileSelect" accept=".zip" />
        <p>拖拽ZIP文件到此處或點擊選擇文件</p>
        <small>ZIP文件應包含：images文件夾 + vocabularies.json</small>
      </div>

      <!-- 列表基本信息 -->
      <div class="list-info-form">
        <input v-model="newList.name" placeholder="列表名稱" />
        <textarea v-model="newList.description" placeholder="列表描述"></textarea>
        <select v-model="newList.grade">
          <option value="S1">中一</option>
          <option value="S2">中二</option>
          <option value="S3">中三</option>
        </select>
        <input v-model="newList.subject" placeholder="主題分類" />
      </div>

      <!-- 操作按鈕 -->
      <div class="modal-actions">
        <button @click="uploadAndCreate" class="btn-primary">上傳並創建</button>
        <button @click="showUploadModal = false" class="btn-cancel">取消</button>
      </div>
    </div>
  </div>
</template>
```

## 🔧 API設計

### 學生端API

#### 1. 獲取學生的詞彙列表
```javascript
GET /api/student/vocabulary-lists

Response: {
  "success": true,
  "data": {
    "lists": [
      {
        "id": 1,
        "name": "中一上學期詞彙",
        "description": "中一上學期必學詞彙",
        "grade": "S1",
        "subject": "基礎詞彙",
        "total_words": 15,
        "progress_percentage": 60,
        "due_date": "2025-09-01T00:00:00Z",
        "assigned_at": "2025-08-01T10:00:00Z"
      }
    ]
  }
}
```

#### 2. 獲取特定列表的詞彙
```javascript
GET /api/student/vocabulary-lists/:listId/vocabularies

Response: {
  "success": true,
  "data": {
    "list_info": {
      "id": 1,
      "name": "中一上學期詞彙",
      "description": "中一上學期必學詞彙"
    },
    "vocabularies": [
      {
        "id": 1,
        "text": "festival",
        "phonetic": "/ˈfes.tɪ.vəl/",
        "translation_zh_tw": "節日",
        "order_index": 1,
        "status": "learning",
        "progress": 60
      }
    ]
  }
}
```

### 教師端API

#### 1. 創建詞彙列表
```javascript
POST /api/teacher/vocabulary-lists
Body: {
  "name": "節日主題詞彙",
  "description": "關於節日的詞彙學習",
  "grade": "S1",
  "subject": "節日"
}
```

#### 2. 上傳ZIP文件
```javascript
POST /api/teacher/vocabulary-lists/upload
Content-Type: multipart/form-data
Body: {
  "zipFile": [ZIP文件],
  "name": "新詞彙列表",
  "description": "描述",
  "grade": "S1",
  "subject": "主題"
}
```

#### 3. 分配列表給學生
```javascript
POST /api/teacher/vocabulary-lists/:listId/assign
Body: {
  "student_ids": [1, 2, 3],
  "due_date": "2025-09-01T00:00:00Z"
}
```

## 📝 實施計劃

### 階段1：數據庫架構升級 (1天)
1. 創建新表結構
2. 編寫數據遷移腳本
3. 測試數據完整性

### 階段2：學生端界面重構 (2天)
1. 創建詞彙列表選擇頁面
2. 修改現有學習頁面
3. 更新路由和導航

### 階段3：教師端管理界面 (2天)
1. 詞彙列表管理頁面
2. ZIP文件上傳功能
3. 學生分配管理

### 階段4：整合測試 (1天)
1. 完整流程測試
2. 數據一致性驗證
3. 用戶體驗優化

## ✅ 成功標準

### 功能標準
- [ ] 學生可以選擇詞彙列表進行學習
- [ ] 教師可以創建和管理詞彙列表
- [ ] ZIP文件上傳功能正常工作
- [ ] 列表分配和進度追蹤準確

### 技術標準
- [ ] 數據庫查詢性能良好
- [ ] 文件上傳處理穩定
- [ ] API響應時間合理
- [ ] 前端界面響應式設計

### 用戶體驗標準
- [ ] 學習流程直觀易懂
- [ ] 管理操作簡單高效
- [ ] 錯誤處理友好完善
- [ ] 整體設計風格一致
