const database = require('./config/database');

async function checkDatabase() {
    try {
        await database.connect();
        console.log('Connected to database');
        
        const lists = await database.all('SELECT * FROM vocabulary_lists ORDER BY id DESC LIMIT 5');
        console.log('Recent vocabulary lists:');
        console.log(lists);
        
        process.exit(0);
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
}

checkDatabase();
