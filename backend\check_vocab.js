const database = require('./config/database');

async function checkVocabularies() {
    try {
        await database.connect();
        
        // 查找最近創建的詞彙列表
        const recentLists = await database.all(`
            SELECT id, name, created_at 
            FROM vocabulary_lists 
            ORDER BY created_at DESC 
            LIMIT 5
        `);
        
        console.log('最近的詞彙列表:');
        recentLists.forEach(list => {
            console.log(`ID: ${list.id}, 名稱: ${list.name}, 創建時間: ${list.created_at}`);
        });
        
        if (recentLists.length > 0) {
            const listId = recentLists[0].id;
            console.log(`\n檢查列表 ${listId} 中的詞彙:`);
            
            // 查找該列表中的詞彙
            const vocabularies = await database.all(`
                SELECT v.text, v.definition_en, v.translation_zh_tw
                FROM vocabularies v
                JOIN vocabulary_list_items vli ON v.id = vli.vocabulary_id
                WHERE vli.list_id = ?
                ORDER BY vli.order_index
                LIMIT 5
            `, [listId]);
            
            vocabularies.forEach(vocab => {
                console.log(`詞彙: ${vocab.text}`);
                console.log(`  中文: ${vocab.translation_zh_tw}`);
                console.log(`  英文解釋: ${vocab.definition_en || '(空)'}`);
                console.log('---');
            });
        }
        
        await database.close();
    } catch (error) {
        console.error('錯誤:', error);
    }
}

checkVocabularies();
