-- 詞彙列表管理系統 - 數據庫架構升級
-- 創建日期: 2025-08-02
-- 版本: 1.0.0

-- 詞彙列表主表
CREATE TABLE vocabulary_lists (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    grade TEXT,
    subject TEXT,
    total_words INTEGER DEFAULT 0,
    created_by INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 詞彙列表內容表
CREATE TABLE vocabulary_list_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    list_id INTEGER NOT NULL,
    vocabulary_id INTEGER NOT NULL,
    order_index INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (list_id) REFERENCES vocabulary_lists(id) ON DELETE CASCADE,
    FOREIGN KEY (vocabulary_id) REFERENCES vocabularies(id) ON DELETE CASCADE,
    UNIQUE(list_id, vocabulary_id)
);

-- 學生詞彙列表分配表
CREATE TABLE user_vocabulary_lists (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    list_id INTEGER NOT NULL,
    assigned_by INTEGER NOT NULL,
    assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    due_date DATETIME,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'archived')),
    progress_percentage INTEGER DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (list_id) REFERENCES vocabulary_lists(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id),
    UNIQUE(user_id, list_id)
);

-- 創建索引以提升查詢性能
CREATE INDEX idx_vocabulary_lists_created_by ON vocabulary_lists(created_by);
CREATE INDEX idx_vocabulary_lists_grade ON vocabulary_lists(grade);
CREATE INDEX idx_vocabulary_list_items_list_id ON vocabulary_list_items(list_id);
CREATE INDEX idx_vocabulary_list_items_order ON vocabulary_list_items(list_id, order_index);
CREATE INDEX idx_user_vocabulary_lists_user_id ON user_vocabulary_lists(user_id);
CREATE INDEX idx_user_vocabulary_lists_status ON user_vocabulary_lists(user_id, status);

-- 插入測試數據
-- 創建默認詞彙列表
INSERT INTO vocabulary_lists (name, description, grade, subject, created_by) VALUES
('中一基礎詞彙', '中一學生基礎詞彙學習', 'S1', '基礎詞彙', 1),
('中二進階詞彙', '中二學生進階詞彙學習', 'S2', '進階詞彙', 1),
('節日主題詞彙', '關於節日的詞彙學習', 'S1', '節日', 1);

-- 更新詞彙列表的詞彙總數（將在遷移腳本中動態計算）
