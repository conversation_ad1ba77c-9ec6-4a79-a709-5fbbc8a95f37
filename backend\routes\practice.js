const express = require('express');
const router = express.Router();
const database = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');

// 中間件：確保只有學生可以訪問
router.use(authenticateToken);
router.use(requireRole(['student']));

/**
 * 生成多選題
 */
function generateMultipleChoice(targetVocab, allVocabs) {
    const correctAnswer = targetVocab.translation_zh_tw;
    
    // 從其他詞彙中選擇3個錯誤答案
    const wrongAnswers = allVocabs
        .filter(v => v.id !== targetVocab.id && v.translation_zh_tw !== correctAnswer)
        .map(v => v.translation_zh_tw)
        .sort(() => 0.5 - Math.random())
        .slice(0, 3);
    
    const options = [correctAnswer, ...wrongAnswers].sort(() => 0.5 - Math.random());
    
    return {
        id: `mc_${targetVocab.id}_${Date.now()}`,
        vocabulary_id: targetVocab.id,
        type: 'multiple_choice',
        question: `「${targetVocab.text}」的中文意思是？`,
        options: options,
        correct_answer: correctAnswer,
        word_info: {
            text: targetVocab.text,
            phonetic: targetVocab.phonetic
        }
    };
}

/**
 * 生成填空題
 */
function generateFillBlank(targetVocab) {
    const sentence = targetVocab.sentence_en;
    const wordToHide = targetVocab.text;
    
    // 將目標單詞替換為空白
    const blankedSentence = sentence.replace(
        new RegExp(`\\b${wordToHide}\\b`, 'gi'), 
        '______'
    );
    
    return {
        id: `fb_${targetVocab.id}_${Date.now()}`,
        vocabulary_id: targetVocab.id,
        type: 'fill_blank',
        question: `請填入正確的英文單詞：${blankedSentence}`,
        correct_answer: wordToHide.toLowerCase(),
        hint: `中文意思：${targetVocab.translation_zh_tw}`,
        phonetic: targetVocab.phonetic
    };
}

/**
 * 生成拼寫練習
 */
function generateTyping(targetVocab) {
    return {
        id: `tp_${targetVocab.id}_${Date.now()}`,
        vocabulary_id: targetVocab.id,
        type: 'typing',
        question: `請拼寫「${targetVocab.translation_zh_tw}」的英文：`,
        correct_answer: targetVocab.text.toLowerCase(),
        phonetic: targetVocab.phonetic,
        hint: `音標：${targetVocab.phonetic}`,
        definition: targetVocab.definition_en
    };
}

/**
 * GET /api/practice/questions
 * 獲取練習題目
 */
router.get('/questions', async (req, res) => {
    try {
        const userId = req.user.id;
        const { type = 'multiple_choice', count = 5, mode, listId } = req.query;

        const questionCount = Math.min(parseInt(count), 10); // 最多10題

        let vocabulariesQuery;
        let queryParams;

        if (mode === 'review') {
            // 複習模式：選擇需要複習的詞彙（準確率低或進度低）
            if (listId) {
                vocabulariesQuery = `
                    SELECT
                        v.id,
                        v.text,
                        v.phonetic,
                        v.part_of_speech,
                        v.definition_en,
                        v.translation_zh_tw,
                        v.sentence_en,
                        v.image_path,
                        uv.status,
                        uv.progress,
                        uv.practice_count,
                        uv.correct_count,
                        CASE
                            WHEN uv.practice_count > 0 THEN (uv.correct_count * 100.0 / uv.practice_count)
                            ELSE 0
                        END as accuracy
                    FROM vocabularies v
                    INNER JOIN vocabulary_list_items vli ON v.id = vli.vocabulary_id
                    INNER JOIN user_vocabularies uv ON v.id = uv.vocabulary_id
                    WHERE uv.user_id = ? AND vli.list_id = ?
                    AND (
                        (uv.practice_count > 0 AND (uv.correct_count * 100.0 / uv.practice_count) < 70)
                        OR uv.progress < 30
                        OR uv.status = 'review'
                    )
                    ORDER BY accuracy ASC, uv.progress ASC, RANDOM()
                    LIMIT ?
                `;
                queryParams = [userId, listId, questionCount];
            } else {
                vocabulariesQuery = `
                    SELECT
                        v.id,
                        v.text,
                        v.phonetic,
                        v.part_of_speech,
                        v.definition_en,
                        v.translation_zh_tw,
                        v.sentence_en,
                        v.image_path,
                        uv.status,
                        uv.progress,
                        uv.practice_count,
                        uv.correct_count,
                        CASE
                            WHEN uv.practice_count > 0 THEN (uv.correct_count * 100.0 / uv.practice_count)
                            ELSE 0
                        END as accuracy
                    FROM vocabularies v
                    INNER JOIN user_vocabularies uv ON v.id = uv.vocabulary_id
                    WHERE uv.user_id = ?
                    AND (
                        (uv.practice_count > 0 AND (uv.correct_count * 100.0 / uv.practice_count) < 70)
                        OR uv.progress < 30
                        OR uv.status = 'review'
                    )
                    ORDER BY accuracy ASC, uv.progress ASC, RANDOM()
                    LIMIT ?
                `;
                queryParams = [userId, questionCount];
            }
        } else {
            // 普通模式：優先選擇需要練習的詞彙
            if (listId) {
                vocabulariesQuery = `
                    SELECT
                        v.id,
                        v.text,
                        v.phonetic,
                        v.part_of_speech,
                        v.definition_en,
                        v.translation_zh_tw,
                        v.sentence_en,
                        v.image_path,
                        uv.status,
                        uv.progress
                    FROM vocabularies v
                    INNER JOIN vocabulary_list_items vli ON v.id = vli.vocabulary_id
                    LEFT JOIN user_vocabularies uv ON v.id = uv.vocabulary_id AND uv.user_id = ?
                    WHERE vli.list_id = ?
                    ORDER BY
                        CASE
                            WHEN uv.status = 'assigned' OR uv.status IS NULL THEN 1
                            WHEN uv.status = 'learning' THEN 2
                            WHEN uv.status = 'review' THEN 3
                            ELSE 4
                        END,
                        RANDOM()
                    LIMIT ?
                `;
                queryParams = [userId, listId, questionCount];
            } else {
                vocabulariesQuery = `
                    SELECT
                        v.id,
                        v.text,
                        v.phonetic,
                        v.part_of_speech,
                        v.definition_en,
                        v.translation_zh_tw,
                        v.sentence_en,
                        v.image_path,
                        uv.status,
                        uv.progress
                    FROM vocabularies v
                    INNER JOIN user_vocabularies uv ON v.id = uv.vocabulary_id
                    WHERE uv.user_id = ?
                    ORDER BY
                        CASE
                            WHEN uv.status = 'assigned' THEN 1
                            WHEN uv.status = 'learning' THEN 2
                            WHEN uv.status = 'review' THEN 3
                            ELSE 4
                        END,
                        RANDOM()
                    LIMIT ?
                `;
                queryParams = [userId, questionCount];
            }
        }

        const vocabularies = await database.all(vocabulariesQuery, queryParams);

        if (vocabularies.length === 0) {
            return res.status(404).json({
                success: false,
                message: '沒有可用的詞彙進行練習'
            });
        }

        // 生成練習題目
        const questions = [];
        const practiceSessionId = `session_${userId}_${Date.now()}`;

        for (const vocab of vocabularies) {
            let question;
            
            switch (type) {
                case 'multiple_choice':
                    question = generateMultipleChoice(vocab, vocabularies);
                    break;
                case 'fill_blank':
                    question = generateFillBlank(vocab);
                    break;
                case 'typing':
                    question = generateTyping(vocab);
                    break;
                default:
                    question = generateMultipleChoice(vocab, vocabularies);
            }
            
            questions.push(question);
        }

        res.json({
            success: true,
            data: {
                practice_session_id: practiceSessionId,
                questions: questions,
                time_limit: 300 // 5分鐘
            }
        });

    } catch (error) {
        console.error('生成練習題目失敗:', error);
        res.status(500).json({
            success: false,
            message: '生成練習題目失敗'
        });
    }
});

/**
 * 更新詞彙學習進度
 */
async function updateVocabularyProgress(userId, vocabularyId, isCorrect, practiceType) {
    try {
        // 獲取當前進度
        const current = await database.get(`
            SELECT status, progress, practice_count, correct_count
            FROM user_vocabularies
            WHERE user_id = ? AND vocabulary_id = ?
        `, [userId, vocabularyId]);

        if (!current) return null;

        let progressChange = 0;
        
        if (isCorrect) {
            // 根據練習類型給予不同分數
            const typeMultiplier = {
                'multiple_choice': 1.0,
                'fill_blank': 1.2,
                'typing': 1.5
            };
            
            progressChange = 10 * (typeMultiplier[practiceType] || 1.0);
        } else {
            // 錯誤時減少進度，但不低於0
            progressChange = -5;
        }
        
        const newProgress = Math.max(0, Math.min(100, current.progress + progressChange));
        
        // 狀態更新邏輯
        let newStatus = current.status;
        if (newProgress >= 80) {
            newStatus = 'mastered';
        } else if (newProgress >= 30) {
            newStatus = 'learning';
        } else if (newProgress < 30 && current.progress >= 30) {
            newStatus = 'review';
        }

        // 更新數據庫
        await database.run(`
            UPDATE user_vocabularies 
            SET 
                status = ?,
                progress = ?,
                practice_count = practice_count + 1,
                correct_count = correct_count + ?,
                last_practiced = datetime('now')
            WHERE user_id = ? AND vocabulary_id = ?
        `, [newStatus, newProgress, isCorrect ? 1 : 0, userId, vocabularyId]);

        return { 
            newProgress, 
            newStatus, 
            progressChange 
        };

    } catch (error) {
        console.error('更新詞彙進度失敗:', error);
        return null;
    }
}

/**
 * POST /api/practice/submit
 * 提交練習結果
 */
router.post('/submit', async (req, res) => {
    try {
        const userId = req.user.id;
        const { practice_session_id, answers } = req.body;

        if (!practice_session_id || !answers || !Array.isArray(answers)) {
            return res.status(400).json({
                success: false,
                message: '請求數據格式錯誤'
            });
        }

        let totalQuestions = answers.length;
        let correctAnswers = 0;
        let totalTime = 0;
        const vocabularyUpdates = [];

        // 處理每個答案
        for (const answer of answers) {
            const { question_id, vocabulary_id, user_answer, is_correct, time_spent = 0 } = answer;
            
            if (is_correct) correctAnswers++;
            totalTime += time_spent;

            // 記錄練習結果
            await database.run(`
                INSERT INTO practice_records 
                (user_id, vocabulary_id, practice_type, is_correct, response, time_spent)
                VALUES (?, ?, ?, ?, ?, ?)
            `, [
                userId,
                vocabulary_id,
                question_id.split('_')[0] === 'mc' ? 'multiple_choice' : 
                question_id.split('_')[0] === 'fb' ? 'fill_blank' : 'typing',
                is_correct,
                user_answer,
                time_spent
            ]);

            // 更新詞彙進度
            const practiceType = question_id.split('_')[0] === 'mc' ? 'multiple_choice' : 
                               question_id.split('_')[0] === 'fb' ? 'fill_blank' : 'typing';
            
            const progressUpdate = await updateVocabularyProgress(
                userId, 
                vocabulary_id, 
                is_correct, 
                practiceType
            );

            if (progressUpdate) {
                vocabularyUpdates.push({
                    vocabulary_id: vocabulary_id,
                    new_status: progressUpdate.newStatus,
                    progress_change: progressUpdate.progressChange,
                    new_progress: progressUpdate.newProgress
                });
            }
        }

        const accuracy = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;

        res.json({
            success: true,
            data: {
                session_results: {
                    total_questions: totalQuestions,
                    correct_answers: correctAnswers,
                    accuracy: accuracy,
                    total_time: totalTime,
                    score: accuracy
                },
                vocabulary_updates: vocabularyUpdates
            }
        });

    } catch (error) {
        console.error('提交練習結果失敗:', error);
        res.status(500).json({
            success: false,
            message: '提交練習結果失敗'
        });
    }
});

module.exports = router;
