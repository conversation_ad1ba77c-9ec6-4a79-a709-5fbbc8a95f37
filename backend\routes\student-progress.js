const express = require('express');
const router = express.Router();
const database = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');

// 獲取教師的所有學生進度概覽
router.get('/students-progress', authenticateToken, requireRole(['teacher']), async (req, res) => {
    try {
        const teacherId = req.user.id;
        
        // 獲取該教師分配的所有學生及其進度
        const studentsProgress = await database.all(`
            SELECT DISTINCT
                u.id as student_id,
                u.username,
                u.full_name,
                u.grade,
                u.class_id,
                COUNT(DISTINCT uvl.list_id) as assigned_lists,
                COUNT(DISTINCT CASE WHEN uvl.status = 'completed' THEN uvl.list_id END) as completed_lists,
                AVG(uvl.progress_percentage) as avg_progress,
                MAX(pr.created_at) as last_activity
            FROM users u
            LEFT JOIN user_vocabulary_lists uvl ON u.id = uvl.user_id AND uvl.assigned_by = ?
            LEFT JOIN practice_records pr ON u.id = pr.user_id
            WHERE u.role = 'student'
            GROUP BY u.id, u.username, u.full_name, u.grade, u.class_id
            HAVING assigned_lists > 0
            ORDER BY u.grade, u.full_name
        `, [teacherId]);

        res.json({
            success: true,
            data: studentsProgress.map(student => ({
                ...student,
                avg_progress: Math.round(student.avg_progress || 0),
                last_activity: student.last_activity || null
            }))
        });

    } catch (error) {
        console.error('獲取學生進度失敗:', error);
        res.status(500).json({
            success: false,
            message: '獲取學生進度失敗'
        });
    }
});

// 獲取特定學生的詳細進度
router.get('/student-progress/:studentId', authenticateToken, requireRole(['teacher']), async (req, res) => {
    try {
        const teacherId = req.user.id;
        const studentId = req.params.studentId;

        // 驗證學生是否由該教師分配
        const assignmentCheck = await database.get(`
            SELECT COUNT(*) as count 
            FROM user_vocabulary_lists 
            WHERE user_id = ? AND assigned_by = ?
        `, [studentId, teacherId]);

        if (assignmentCheck.count === 0) {
            return res.status(403).json({
                success: false,
                message: '無權查看該學生進度'
            });
        }

        // 獲取學生基本信息
        const student = await database.get(`
            SELECT id, username, full_name, grade, class_id, last_login
            FROM users 
            WHERE id = ? AND role = 'student'
        `, [studentId]);

        if (!student) {
            return res.status(404).json({
                success: false,
                message: '學生不存在'
            });
        }

        // 獲取學生的詞彙列表進度
        const listsProgress = await database.all(`
            SELECT 
                vl.id as list_id,
                vl.name as list_name,
                vl.description,
                vl.grade as list_grade,
                vl.subject,
                vl.total_words,
                uvl.assigned_at,
                uvl.due_date,
                uvl.status as list_status,
                uvl.progress_percentage,
                COUNT(DISTINCT vli.vocabulary_id) as total_vocabularies,
                COUNT(DISTINCT CASE WHEN uv.status = 'mastered' THEN uv.vocabulary_id END) as mastered_count,
                COUNT(DISTINCT CASE WHEN uv.status = 'learning' THEN uv.vocabulary_id END) as learning_count,
                COUNT(DISTINCT pr.id) as total_practices,
                COUNT(DISTINCT CASE WHEN pr.is_correct = 1 THEN pr.id END) as correct_practices
            FROM user_vocabulary_lists uvl
            JOIN vocabulary_lists vl ON uvl.list_id = vl.id
            LEFT JOIN vocabulary_list_items vli ON vl.id = vli.list_id
            LEFT JOIN user_vocabularies uv ON vli.vocabulary_id = uv.vocabulary_id AND uv.user_id = uvl.user_id
            LEFT JOIN practice_records pr ON vli.vocabulary_id = pr.vocabulary_id AND pr.user_id = uvl.user_id
            WHERE uvl.user_id = ? AND uvl.assigned_by = ?
            GROUP BY vl.id, vl.name, vl.description, vl.grade, vl.subject, vl.total_words, 
                     uvl.assigned_at, uvl.due_date, uvl.status, uvl.progress_percentage
            ORDER BY uvl.assigned_at DESC
        `, [studentId, teacherId]);

        // 獲取最近的練習記錄
        const recentPractices = await database.all(`
            SELECT 
                pr.id,
                pr.practice_type,
                pr.is_correct,
                pr.time_spent,
                pr.created_at,
                v.text as word,
                v.translation_zh_tw,
                vl.name as list_name
            FROM practice_records pr
            JOIN vocabularies v ON pr.vocabulary_id = v.id
            JOIN vocabulary_list_items vli ON v.id = vli.vocabulary_id
            JOIN vocabulary_lists vl ON vli.list_id = vl.id
            JOIN user_vocabulary_lists uvl ON vl.id = uvl.list_id AND uvl.user_id = pr.user_id
            WHERE pr.user_id = ? AND uvl.assigned_by = ?
            ORDER BY pr.created_at DESC
            LIMIT 20
        `, [studentId, teacherId]);

        // 計算總體統計
        const totalStats = {
            total_lists: listsProgress.length,
            completed_lists: listsProgress.filter(list => list.list_status === 'completed').length,
            total_vocabularies: listsProgress.reduce((sum, list) => sum + (list.total_vocabularies || 0), 0),
            mastered_vocabularies: listsProgress.reduce((sum, list) => sum + (list.mastered_count || 0), 0),
            total_practices: listsProgress.reduce((sum, list) => sum + (list.total_practices || 0), 0),
            correct_practices: listsProgress.reduce((sum, list) => sum + (list.correct_practices || 0), 0),
            accuracy_rate: 0
        };

        if (totalStats.total_practices > 0) {
            totalStats.accuracy_rate = Math.round((totalStats.correct_practices / totalStats.total_practices) * 100);
        }

        res.json({
            success: true,
            data: {
                student,
                totalStats,
                listsProgress,
                recentPractices
            }
        });

    } catch (error) {
        console.error('獲取學生詳細進度失敗:', error);
        res.status(500).json({
            success: false,
            message: '獲取學生詳細進度失敗'
        });
    }
});

module.exports = router;
