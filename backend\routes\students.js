const express = require('express');
const router = express.Router();
const database = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');

// 中間件：確保只有學生可以訪問
router.use(authenticateToken);
router.use(requireRole(['student']));

/**
 * GET /api/student/vocabularies
 * 獲取學生的詞彙列表
 */
router.get('/vocabularies', async (req, res) => {
    try {
        const userId = req.user.id;

        // 獲取學生分配的詞彙列表
        const vocabularies = await database.all(`
            SELECT 
                v.id,
                v.text,
                v.phonetic,
                v.part_of_speech,
                v.definition_en,
                v.translation_zh_tw,
                v.sentence_en,
                v.image_path,
                uv.status,
                uv.progress,
                uv.practice_count,
                uv.correct_count,
                uv.last_practiced
            FROM vocabularies v
            INNER JOIN user_vocabularies uv ON v.id = uv.vocabulary_id
            WHERE uv.user_id = ?
            ORDER BY uv.created_at ASC
        `, [userId]);

        // 計算統計信息
        const stats = {
            assigned: vocabularies.filter(v => v.status === 'assigned').length,
            learning: vocabularies.filter(v => v.status === 'learning').length,
            mastered: vocabularies.filter(v => v.status === 'mastered').length,
            review: vocabularies.filter(v => v.status === 'review').length
        };

        res.json({
            success: true,
            data: {
                vocabularies: vocabularies,
                total: vocabularies.length,
                stats: stats
            }
        });

    } catch (error) {
        console.error('獲取學生詞彙列表失敗:', error);
        res.status(500).json({
            success: false,
            message: '獲取詞彙列表失敗'
        });
    }
});

/**
 * GET /api/student/vocabulary/:id
 * 獲取特定詞彙的詳細信息
 */
router.get('/vocabulary/:id', async (req, res) => {
    try {
        const userId = req.user.id;
        const vocabularyId = req.params.id;

        // 獲取詞彙詳情
        const vocabulary = await database.get(`
            SELECT 
                v.id,
                v.text,
                v.phonetic,
                v.part_of_speech,
                v.definition_en,
                v.translation_zh_tw,
                v.sentence_en,
                v.image_path
            FROM vocabularies v
            INNER JOIN user_vocabularies uv ON v.id = uv.vocabulary_id
            WHERE v.id = ? AND uv.user_id = ?
        `, [vocabularyId, userId]);

        if (!vocabulary) {
            return res.status(404).json({
                success: false,
                message: '找不到該詞彙或您沒有權限訪問'
            });
        }

        // 獲取學習進度
        const userProgress = await database.get(`
            SELECT 
                status,
                progress,
                practice_count,
                correct_count,
                last_practiced
            FROM user_vocabularies
            WHERE user_id = ? AND vocabulary_id = ?
        `, [userId, vocabularyId]);

        res.json({
            success: true,
            data: {
                vocabulary: vocabulary,
                user_progress: userProgress
            }
        });

    } catch (error) {
        console.error('獲取詞彙詳情失敗:', error);
        res.status(500).json({
            success: false,
            message: '獲取詞彙詳情失敗'
        });
    }
});

// 獲取學生統計數據
router.get('/statistics', async (req, res) => {
    try {
        const userId = req.user.id;

        // 獲取總體統計
        const overallStats = await database.get(`
            SELECT
                COUNT(*) as total_vocabularies,
                SUM(CASE WHEN uv.status = 'mastered' THEN 1 ELSE 0 END) as mastered_count,
                SUM(CASE WHEN uv.status = 'learning' THEN 1 ELSE 0 END) as learning_count,
                SUM(CASE WHEN uv.status = 'assigned' THEN 1 ELSE 0 END) as assigned_count,
                CASE
                    WHEN SUM(uv.practice_count) > 0 THEN
                        ROUND((SUM(uv.correct_count) * 100.0 / SUM(uv.practice_count)), 1)
                    ELSE 0
                END as overall_accuracy
            FROM user_vocabularies uv
            WHERE uv.user_id = ?
        `, [userId]);

        // 獲取最近7天的練習活動
        const recentActivity = await database.all(`
            SELECT
                DATE(pr.created_at) as date,
                COUNT(*) as practices,
                ROUND(AVG(CASE WHEN pr.is_correct = 1 THEN 100.0 ELSE 0.0 END), 1) as accuracy
            FROM practice_records pr
            WHERE pr.user_id = ?
            AND pr.created_at >= datetime('now', '-7 days')
            GROUP BY DATE(pr.created_at)
            ORDER BY date DESC
        `, [userId]);

        res.json({
            success: true,
            data: {
                overall_stats: overallStats || {
                    total_vocabularies: 0,
                    mastered_count: 0,
                    learning_count: 0,
                    assigned_count: 0,
                    overall_accuracy: 0
                },
                recent_activity: recentActivity || []
            }
        });

    } catch (error) {
        console.error('獲取統計數據失敗:', error);
        res.status(500).json({
            success: false,
            message: '獲取統計數據失敗'
        });
    }
});

module.exports = router;
