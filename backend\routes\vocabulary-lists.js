const express = require('express');
const router = express.Router();
const database = require('../config/database');
const { authenticateToken, requireRole } = require('../middleware/auth');
const multer = require('multer');
const AdmZip = require('adm-zip');
const path = require('path');
const fs = require('fs');

// Multer配置用於文件上傳
const upload = multer({
    dest: 'uploads/temp/',
    limits: {
        fileSize: 50 * 1024 * 1024 // 50MB
    },
    fileFilter: (req, file, cb) => {
        console.log('File filter called:', file);
        if (file.mimetype === 'application/zip' || file.originalname.toLowerCase().endsWith('.zip')) {
            console.log('File accepted');
            cb(null, true);
        } else {
            console.log('File rejected:', file.mimetype, file.originalname);
            cb(new Error('只允許上傳ZIP文件'));
        }
    }
});

// ==================== 教師端API ====================
// 注意：學生相關路由已移至 students.js，此文件現在只處理教師功能



// 獲取教師創建的詞彙列表
router.get('/vocabulary-lists', authenticateToken, requireRole(['teacher']), async (req, res) => {
    try {
        const teacherId = req.user.id;

        const lists = await database.all(`
            SELECT
                vl.*,
                COUNT(DISTINCT uvl.user_id) as assigned_students,
                COUNT(DISTINCT vli.vocabulary_id) as total_words
            FROM vocabulary_lists vl
            LEFT JOIN user_vocabulary_lists uvl ON vl.id = uvl.list_id AND uvl.status = 'active'
            LEFT JOIN vocabulary_list_items vli ON vl.id = vli.list_id
            WHERE vl.created_by = ?
            GROUP BY vl.id
            ORDER BY vl.created_at DESC
        `, [teacherId]);

        res.json({
            success: true,
            lists: lists
        });

    } catch (error) {
        console.error('獲取教師詞彙列表失敗:', error);
        res.status(500).json({
            success: false,
            message: '獲取詞彙列表失敗'
        });
    }
});

// 創建新的詞彙列表
router.post('/vocabulary-lists', authenticateToken, requireRole(['teacher']), async (req, res) => {
    try {
        const teacherId = req.user.id;
        const { name, description, grade, subject } = req.body;

        if (!name || !grade || !subject) {
            return res.status(400).json({
                success: false,
                message: '請提供列表名稱、年級和科目'
            });
        }

        const result = await database.run(`
            INSERT INTO vocabulary_lists (name, description, grade, subject, created_by, created_at)
            VALUES (?, ?, ?, ?, ?, datetime('now'))
        `, [name, description, grade, subject, teacherId]);

        res.json({
            success: true,
            data: {
                id: result.lastID,
                name,
                description,
                grade,
                subject
            }
        });

    } catch (error) {
        console.error('創建詞彙列表失敗:', error);
        res.status(500).json({
            success: false,
            message: '創建詞彙列表失敗'
        });
    }
});

// 更新詞彙列表
router.put('/vocabulary-lists/:listId', authenticateToken, requireRole(['teacher']), async (req, res) => {
    try {
        const teacherId = req.user.id;
        const listId = req.params.listId;
        const { name, description, grade, subject } = req.body;

        // 驗證列表所有權
        const list = await database.get(`
            SELECT * FROM vocabulary_lists WHERE id = ? AND created_by = ?
        `, [listId, teacherId]);

        if (!list) {
            return res.status(404).json({
                success: false,
                message: '詞彙列表不存在或無權限'
            });
        }

        await database.run(`
            UPDATE vocabulary_lists
            SET name = ?, description = ?, grade = ?, subject = ?
            WHERE id = ? AND created_by = ?
        `, [name, description, grade, subject, listId, teacherId]);

        res.json({
            success: true,
            message: '詞彙列表更新成功'
        });

    } catch (error) {
        console.error('更新詞彙列表失敗:', error);
        res.status(500).json({
            success: false,
            message: '更新詞彙列表失敗'
        });
    }
});

// 刪除詞彙列表
router.delete('/vocabulary-lists/:listId', authenticateToken, requireRole(['teacher']), async (req, res) => {
    try {
        const teacherId = req.user.id;
        const listId = req.params.listId;

        // 驗證列表所有權
        const list = await database.get(`
            SELECT * FROM vocabulary_lists WHERE id = ? AND created_by = ?
        `, [listId, teacherId]);

        if (!list) {
            return res.status(404).json({
                success: false,
                message: '詞彙列表不存在或無權限'
            });
        }

        // 刪除相關數據（由於外鍵約束，會自動級聯刪除）
        await database.run(`DELETE FROM vocabulary_lists WHERE id = ?`, [listId]);

        res.json({
            success: true,
            message: '詞彙列表刪除成功'
        });

    } catch (error) {
        console.error('刪除詞彙列表失敗:', error);
        res.status(500).json({
            success: false,
            message: '刪除詞彙列表失敗'
        });
    }
});

// 獲取詞彙列表的學生分配狀態
router.get('/vocabulary-lists/:listId/students', authenticateToken, requireRole(['teacher']), async (req, res) => {
    try {
        const teacherId = req.user.id;
        const listId = req.params.listId;

        // 驗證列表所有權
        const list = await database.get(`
            SELECT * FROM vocabulary_lists WHERE id = ? AND created_by = ?
        `, [listId, teacherId]);

        if (!list) {
            return res.status(404).json({
                success: false,
                message: '詞彙列表不存在或無權限'
            });
        }

        // 獲取所有學生及其分配狀態
        const students = await database.all(`
            SELECT
                u.id,
                u.username,
                u.full_name,
                u.grade,
                uvl.assigned_at as assignment_date,
                uvl.due_date,
                uvl.status as assignment_status,
                uvl.progress_percentage,
                CASE WHEN uvl.id IS NOT NULL THEN 1 ELSE 0 END as is_assigned
            FROM users u
            LEFT JOIN user_vocabulary_lists uvl ON u.id = uvl.user_id AND uvl.list_id = ?
            WHERE u.role = 'student'
            ORDER BY u.grade, u.full_name
        `, [listId]);

        res.json({
            success: true,
            students: students
        });

    } catch (error) {
        console.error('獲取學生分配狀態失敗:', error);
        res.status(500).json({
            success: false,
            message: '獲取學生分配狀態失敗'
        });
    }
});

// 分配詞彙列表給學生
router.post('/vocabulary-lists/:listId/assign', authenticateToken, requireRole(['teacher']), async (req, res) => {
    try {
        const teacherId = req.user.id;
        const listId = req.params.listId;
        const { student_ids, due_date, status = 'active' } = req.body;

        // 驗證列表所有權
        const list = await database.get(`
            SELECT * FROM vocabulary_lists WHERE id = ? AND created_by = ?
        `, [listId, teacherId]);

        if (!list) {
            return res.status(404).json({
                success: false,
                message: '詞彙列表不存在或無權限'
            });
        }

        if (!student_ids || !Array.isArray(student_ids) || student_ids.length === 0) {
            return res.status(400).json({
                success: false,
                message: '請選擇要分配的學生'
            });
        }

        // 先刪除現有分配（重新分配）
        await database.run(`
            DELETE FROM user_vocabulary_lists WHERE list_id = ?
        `, [listId]);

        // 批量插入新分配
        const insertPromises = student_ids.map(studentId => {
            return database.run(`
                INSERT INTO user_vocabulary_lists (user_id, list_id, assigned_by, assigned_at, due_date, status)
                VALUES (?, ?, ?, datetime('now'), ?, ?)
            `, [studentId, listId, teacherId, due_date, status]);
        });

        await Promise.all(insertPromises);

        res.json({
            success: true,
            message: `成功分配詞彙列表給 ${student_ids.length} 個學生`
        });

    } catch (error) {
        console.error('分配詞彙列表失敗:', error);
        res.status(500).json({
            success: false,
            message: '分配詞彙列表失敗'
        });
    }
});

// 測試路由
router.post('/vocabulary-lists/test', (req, res) => {
    console.log('Test route hit');
    res.json({ success: true, message: 'Test route working' });
});

// ZIP文件上傳處理
router.post('/vocabulary-lists/upload', authenticateToken, requireRole(['teacher']), upload.single('zipFile'), async (req, res) => {
    let tempDir = null;

    try {
        console.log('Upload request received');
        console.log('User:', req.user);
        console.log('File:', req.file);
        console.log('Body:', req.body);

        const teacherId = req.user.id;
        const zipFile = req.file;

        if (!zipFile) {
            console.log('No file uploaded');
            return res.status(400).json({
                success: false,
                message: '請上傳ZIP文件'
            });
        }

        // 創建臨時解壓目錄
        tempDir = path.join(__dirname, '../uploads/temp', `extract_${Date.now()}`);
        fs.mkdirSync(tempDir, { recursive: true });

        // 解壓ZIP文件
        console.log('Extracting ZIP file from:', zipFile.path);
        const zip = new AdmZip(zipFile.path);
        zip.extractAllTo(tempDir, true);
        console.log('ZIP extracted to:', tempDir);

        // 查找JSON文件
        const allFiles = fs.readdirSync(tempDir);
        console.log('Files in extracted directory:', allFiles);
        const jsonFiles = allFiles.filter(file => file.toLowerCase().endsWith('.json'));
        console.log('JSON files found:', jsonFiles);
        if (jsonFiles.length === 0) {
            throw new Error('ZIP文件中未找到JSON數據文件');
        }

        const jsonFile = path.join(tempDir, jsonFiles[0]);
        console.log('Reading JSON file:', jsonFile);
        const jsonData = JSON.parse(fs.readFileSync(jsonFile, 'utf8'));
        console.log('JSON data parsed successfully');

        if (!jsonData.vocabularies || !Array.isArray(jsonData.vocabularies)) {
            throw new Error('JSON文件格式錯誤：缺少vocabularies數組');
        }

        // 從JSON文件中讀取列表信息
        console.log('JSON data:', jsonData);
        const name = jsonData.name || '未命名詞彙列表';
        const description = jsonData.description || '';
        const grade = jsonData.grade || 'S1';
        const subject = jsonData.subject || '英語';
        console.log('Parsed data:', { name, description, grade, subject });

        // 開始數據庫事務
        await database.run('BEGIN TRANSACTION');

        try {
            // 創建詞彙列表
            console.log('Creating vocabulary list with:', { name, description, grade, subject, teacherId });
            console.log('About to execute INSERT query...');
            let listResult;
            try {
                listResult = await database.run(`
                    INSERT INTO vocabulary_lists (name, description, grade, subject, created_by, created_at)
                    VALUES (?, ?, ?, ?, ?, datetime('now'))
                `, [name, description, grade, subject, teacherId]);
                console.log('Raw listResult:', listResult);
                console.log('INSERT query completed successfully');
            } catch (listError) {
                console.error('Error creating vocabulary list:', listError);
                throw listError;
            }

            const listId = listResult.id;
            console.log('Created vocabulary list with ID:', listId);
            console.log('listResult object:', listResult);

            if (!listId) {
                throw new Error('創建詞彙列表失敗，無法獲取列表ID');
            }

            // 處理詞彙數據
            console.log('Processing vocabularies:', jsonData.vocabularies);
            console.log('Number of vocabularies:', jsonData.vocabularies ? jsonData.vocabularies.length : 'undefined');
            let orderIndex = 0;

            if (!jsonData.vocabularies || jsonData.vocabularies.length === 0) {
                console.log('No vocabularies to process');
            } else {
                console.log('Starting vocabulary processing loop...');
                for (const vocabData of jsonData.vocabularies) {
                    console.log('Processing vocabulary:', vocabData);
                // 映射 JSON 字段到數據庫字段
                const mappedVocab = {
                    word_id: vocabData.word || vocabData.text,
                    text: vocabData.word,
                    phonetic: vocabData.pronunciation,
                    part_of_speech: vocabData.part_of_speech || 'noun',
                    definition_en: vocabData.definition_en,
                    translation_zh_tw: vocabData.definition,
                    sentence_en: vocabData.example,
                    image_path: vocabData.image
                };

                // 插入或更新詞彙
                let vocabularyId;
                console.log('Looking for existing vocab with word_id:', mappedVocab.word_id);
                const existingVocab = await database.get(`
                    SELECT id FROM vocabularies WHERE word_id = ?
                `, [mappedVocab.word_id]);
                console.log('Existing vocab found:', existingVocab);

                if (existingVocab) {
                    vocabularyId = existingVocab.id;
                    console.log('Using existing vocabulary ID:', vocabularyId);
                    // 更新現有詞彙
                    await database.run(`
                        UPDATE vocabularies SET
                            phonetic = ?, part_of_speech = ?, definition_en = ?,
                            translation_zh_tw = ?, sentence_en = ?, image_path = ?
                        WHERE id = ?
                    `, [
                        mappedVocab.phonetic,
                        mappedVocab.part_of_speech,
                        mappedVocab.definition_en,
                        mappedVocab.translation_zh_tw,
                        mappedVocab.sentence_en,
                        mappedVocab.image_path,
                        vocabularyId
                    ]);
                } else {
                    // 創建新詞彙
                    console.log('Creating new vocabulary with data:', mappedVocab);
                    const vocabResult = await database.run(`
                        INSERT INTO vocabularies (
                            word_id, text, phonetic, part_of_speech, definition_en,
                            translation_zh_tw, sentence_en, image_path
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    `, [
                        mappedVocab.word_id,
                        mappedVocab.text,
                        mappedVocab.phonetic,
                        mappedVocab.part_of_speech,
                        mappedVocab.definition_en,
                        mappedVocab.translation_zh_tw,
                        mappedVocab.sentence_en,
                        mappedVocab.image_path
                    ]);
                    console.log('Vocabulary creation result:', vocabResult);
                    vocabularyId = vocabResult.id;
                    console.log('New vocabulary ID:', vocabularyId);
                }

                // 添加到詞彙列表
                console.log('Adding to vocabulary list:', { listId, vocabularyId, orderIndex });
                await database.run(`
                    INSERT INTO vocabulary_list_items (list_id, vocabulary_id, order_index)
                    VALUES (?, ?, ?)
                `, [listId, vocabularyId, orderIndex++]);
                }
            }

            // 更新列表的詞彙總數
            await database.run(`
                UPDATE vocabulary_lists SET total_words = ? WHERE id = ?
            `, [jsonData.vocabularies.length, listId]);

            // 處理圖片文件
            const imagesDir = path.join(tempDir, 'images');
            if (fs.existsSync(imagesDir)) {
                const targetImagesDir = path.join(__dirname, '../uploads/images');
                fs.mkdirSync(targetImagesDir, { recursive: true });

                const imageFiles = fs.readdirSync(imagesDir);
                for (const imageFile of imageFiles) {
                    const sourcePath = path.join(imagesDir, imageFile);
                    const targetPath = path.join(targetImagesDir, imageFile);
                    fs.copyFileSync(sourcePath, targetPath);
                }
            }

            await database.run('COMMIT');

            res.json({
                success: true,
                message: '詞彙列表上傳成功',
                data: {
                    listId: listId,
                    name: name,
                    vocabularyCount: jsonData.vocabularies.length
                }
            });

        } catch (dbError) {
            await database.run('ROLLBACK');
            throw dbError;
        }

    } catch (error) {
        console.error('ZIP文件上傳處理失敗:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'ZIP文件處理失敗'
        });
    } finally {
        // 清理臨時文件
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }
        if (tempDir && fs.existsSync(tempDir)) {
            fs.rmSync(tempDir, { recursive: true, force: true });
        }
    }
});

module.exports = router;
