# 數據更新操作指南

## 概述

本文檔說明如何更新系統中的詞彙數據，包括題目內容、圖片、翻譯等所有信息。

## 🔄 更新流程

### 步驟 1: 修改數據文件
編輯 `backend/scripts/stage2-data.json` 文件，更改您需要的內容。

### 步驟 2: 清除舊數據
```bash
cd backend/scripts
node clearVocabularyData.js
```

### 步驟 3: 導入新數據
```bash
node createVocabulariesAndAssignments.js stage2-data.json
```

### 步驟 4: 驗證更新結果
```bash
node quickCheck.js
```

## 📝 可更新的內容

### 詞彙基本信息
- **英文單詞** (`text`): 主要的英文詞彙
- **國際音標** (`phonetic`): 發音標記
- **詞性** (`part_of_speech`): noun, verb, adjective 等
- **英文定義** (`definition_en`): 英文解釋
- **中文翻譯** (`translation_zh_tw`): 繁體中文翻譯
- **英文例句** (`sentence_en`): 使用該詞彙的例句

### 圖片設置
- **圖片路徑** (`image_path`): 圖片文件的相對路徑
- 圖片文件需放置在 `backend/uploads/images/` 目錄中

### 學生分配
- **分配對象**: 哪些學生獲得哪些詞彙
- **初始狀態**: `assigned`, `learning`, `mastered`, `review`

## 🖼️ 圖片管理

### 圖片存放位置
```
backend/uploads/images/
├── adventure.png
├── delicious.png
├── friendship.png
└── ...
```

### 圖片路徑設置
在JSON文件中設置相對路徑：
```json
{
  "text": "festival",
  "image_path": "images/festival.png"
}
```

### 訪問URL
前端會自動生成完整URL：
`http://localhost:3000/uploads/images/festival.png`

## 📋 JSON文件結構示例

```json
{
  "metadata": {
    "version": "2.0.0",
    "created_date": "2025-08-01",
    "description": "英語詞彙學習工具 v3 - Stage 2 數據"
  },
  
  "vocabularies": [
    {
      "id": 1,
      "word_id": "vocab_001",
      "text": "festival",
      "phonetic": "/ˈfes.tɪ.vəl/",
      "part_of_speech": "noun",
      "definition_en": "A special day or period...",
      "translation_zh_tw": "節日",
      "sentence_en": "Chinese New Year is the most important festival...",
      "image_path": "images/festival.png"
    }
  ],
  
  "student_vocabulary_assignments": [
    {
      "student_username": "student01",
      "vocabulary_assignments": [
        {
          "word_id": "vocab_001",
          "status": "assigned"
        }
      ]
    }
  ]
}
```

## ⚠️ 重要注意事項

### 數據清除影響
執行清除操作會刪除：
- ✅ 所有詞彙數據
- ✅ 所有學生詞彙分配
- ✅ 所有練習記錄和學習進度

### 不會影響的數據
- ✅ 用戶帳戶信息
- ✅ 系統設置

### 建議
- 在生產環境中使用前，請先在測試環境驗證
- 定期備份數據庫
- 記錄每次更新的內容和時間

## 🛠️ 常用操作命令

### 快速檢查數據
```bash
# 檢查關鍵詞彙
node quickCheck.js

# 檢查所有詞彙的圖片路徑
node -e "
const db = require('../config/database');
(async () => {
  await db.connect();
  const vocabs = await db.all('SELECT text, image_path FROM vocabularies');
  vocabs.forEach(v => console.log(\`\${v.text}: \${v.image_path}\`));
  await db.close();
})()
"
```

### 檢查圖片文件
```bash
# Windows
dir backend\uploads\images

# 檢查特定圖片是否存在
if exist "backend\uploads\images\festival.png" echo "圖片存在" else echo "圖片不存在"
```

## 🔍 故障排除

### 圖片不顯示
1. 檢查圖片文件是否在 `backend/uploads/images/` 目錄
2. 檢查JSON中的 `image_path` 是否正確
3. 確認後端服務器正在運行
4. 檢查圖片URL: `http://localhost:3000/uploads/images/xxx.png`

### 數據未更新
1. 確認已執行清除數據庫操作
2. 確認已重新導入JSON數據
3. 檢查JSON文件語法是否正確
4. 重啟後端服務器

### 常見錯誤
- **JSON語法錯誤**: 檢查逗號、引號、括號是否正確
- **圖片路徑錯誤**: 確保路徑格式為 `images/filename.ext`
- **重複的word_id**: 確保每個詞彙的word_id唯一

## 📞 技術支持

如遇到問題，請檢查：
1. 後端服務器日誌
2. 前端瀏覽器控制台
3. 數據庫連接狀態
4. 文件權限設置

---

**最後更新**: 2025-08-01
**版本**: 1.0.0
