const database = require('../config/database');

async function checkImagePaths() {
    try {
        await database.connect();
        
        const vocabs = await database.all('SELECT text, image_path FROM vocabularies LIMIT 5');
        
        console.log('📋 數據庫中的圖片路徑:');
        vocabs.forEach(v => {
            console.log(`  ${v.text}: ${v.image_path}`);
        });
        
        await database.close();
    } catch (error) {
        console.error('錯誤:', error);
    }
}

// 只有直接運行此腳本時才執行
if (require.main === module) {
    checkImagePaths();
}
