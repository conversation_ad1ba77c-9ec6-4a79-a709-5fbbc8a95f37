const database = require('../config/database');

async function checkTableStructure() {
    try {
        await database.connect();
        
        console.log('📊 檢查數據庫表結構...\n');
        
        // 檢查vocabularies表
        console.log('1️⃣ vocabularies表結構:');
        const vocabInfo = await database.all('PRAGMA table_info(vocabularies)');
        vocabInfo.forEach(col => {
            console.log(`   ${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''}`);
        });
        
        // 檢查user_vocabularies表是否存在
        console.log('\n2️⃣ user_vocabularies表結構:');
        try {
            const userVocabInfo = await database.all('PRAGMA table_info(user_vocabularies)');
            if (userVocabInfo.length > 0) {
                userVocabInfo.forEach(col => {
                    console.log(`   ${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''}`);
                });
            } else {
                console.log('   ❌ user_vocabularies表不存在或為空');
            }
        } catch (error) {
            console.log('   ❌ user_vocabularies表不存在:', error.message);
        }
        
        // 檢查vocabulary_list_items表
        console.log('\n3️⃣ vocabulary_list_items表結構:');
        const listItemsInfo = await database.all('PRAGMA table_info(vocabulary_list_items)');
        listItemsInfo.forEach(col => {
            console.log(`   ${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''}`);
        });
        
        // 測試查詢
        console.log('\n🧪 測試查詢...');
        
        // 測試基本查詢
        const listItems = await database.all(`
            SELECT vli.*, v.text, v.translation_zh_tw
            FROM vocabulary_list_items vli
            JOIN vocabularies v ON vli.vocabulary_id = v.id
            WHERE vli.list_id = 9
            LIMIT 3
        `);
        
        console.log(`   找到 ${listItems.length} 個詞彙項目:`);
        listItems.forEach(item => {
            console.log(`   - ${item.text}: ${item.translation_zh_tw}`);
        });
        
        // 檢查是否有grade列
        const hasGradeColumn = vocabInfo.some(col => col.name === 'grade');
        console.log(`\n📋 vocabularies表是否有grade列: ${hasGradeColumn ? '✅ 有' : '❌ 沒有'}`);
        
        if (!hasGradeColumn) {
            console.log('⚠️  API查詢中引用了不存在的grade列，需要修復');
        }
        
    } catch (error) {
        console.error('❌ 檢查失敗:', error);
    } finally {
        await database.close();
    }
}

if (require.main === module) {
    checkTableStructure();
}

module.exports = { checkTableStructure };
