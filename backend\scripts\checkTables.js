const database = require('../config/database');

async function checkTables() {
    try {
        await database.connect();
        
        const tables = await database.all(`
            SELECT name FROM sqlite_master 
            WHERE type='table' 
            ORDER BY name
        `);
        
        console.log('所有表:');
        tables.forEach(table => {
            console.log(`  - ${table.name}`);
        });
        
        // 檢查詞彙列表相關表
        const vocabTables = tables.filter(t => t.name.includes('vocabulary'));
        console.log('\n詞彙相關表:');
        vocabTables.forEach(table => {
            console.log(`  ✅ ${table.name}`);
        });
        
        // 檢查user_vocabulary_lists表是否存在
        const userVocabListsExists = tables.some(t => t.name === 'user_vocabulary_lists');
        console.log(`\nuser_vocabulary_lists 表存在: ${userVocabListsExists ? '✅' : '❌'}`);
        
        if (userVocabListsExists) {
            const count = await database.get('SELECT COUNT(*) as count FROM user_vocabulary_lists');
            console.log(`user_vocabulary_lists 記錄數: ${count.count}`);
        }
        
    } catch (error) {
        console.error('檢查失敗:', error);
    } finally {
        await database.close();
    }
}

checkTables();
