const database = require('../config/database');

async function cleanupDuplicates() {
    try {
        console.log('🧹 開始清理重複數據...');
        
        // 連接數據庫
        await database.connect();
        console.log('✅ 數據庫連接成功');
        
        // 1. 查找重複的詞彙列表
        console.log('🔍 查找重複的詞彙列表...');
        const duplicateLists = await database.all(`
            SELECT name, grade, subject, COUNT(*) as count
            FROM vocabulary_lists
            GROUP BY name, grade, subject
            HAVING COUNT(*) > 1
        `);
        
        console.log('重複列表:', duplicateLists);
        
        // 2. 清理重複的詞彙列表，保留ID最小的
        for (const duplicate of duplicateLists) {
            console.log(`🧹 清理重複列表: ${duplicate.name}`);
            
            // 獲取所有重複的記錄
            const duplicateRecords = await database.all(`
                SELECT id FROM vocabulary_lists
                WHERE name = ? AND grade = ? AND subject = ?
                ORDER BY id
            `, [duplicate.name, duplicate.grade, duplicate.subject]);
            
            // 保留第一個，刪除其他的
            for (let i = 1; i < duplicateRecords.length; i++) {
                const recordToDelete = duplicateRecords[i];
                
                // 先刪除相關的分配記錄
                await database.run(`
                    DELETE FROM user_vocabulary_lists 
                    WHERE list_id = ?
                `, [recordToDelete.id]);
                
                // 再刪除相關的列表項目
                await database.run(`
                    DELETE FROM vocabulary_list_items 
                    WHERE list_id = ?
                `, [recordToDelete.id]);
                
                // 最後刪除列表本身
                await database.run(`
                    DELETE FROM vocabulary_lists 
                    WHERE id = ?
                `, [recordToDelete.id]);
                
                console.log(`✅ 刪除重複列表 ID: ${recordToDelete.id}`);
            }
        }
        
        // 3. 重新計算詞彙總數
        console.log('📊 重新計算詞彙總數...');
        const lists = await database.all('SELECT id, name FROM vocabulary_lists');
        
        for (const list of lists) {
            const itemCount = await database.get(`
                SELECT COUNT(*) as count 
                FROM vocabulary_list_items 
                WHERE list_id = ?
            `, [list.id]);
            
            await database.run(`
                UPDATE vocabulary_lists 
                SET total_words = ? 
                WHERE id = ?
            `, [itemCount.count, list.id]);
            
            console.log(`✅ 更新列表 "${list.name}" 詞彙總數: ${itemCount.count}`);
        }
        
        // 4. 顯示清理後的統計信息
        const finalStats = await database.get(`
            SELECT 
                (SELECT COUNT(*) FROM vocabulary_lists) as lists_count,
                (SELECT COUNT(*) FROM vocabulary_list_items) as items_count,
                (SELECT COUNT(*) FROM user_vocabulary_lists) as assignments_count
        `);
        
        console.log('\n📊 清理後統計:');
        console.log(`📚 詞彙列表數量: ${finalStats.lists_count}`);
        console.log(`📝 列表項目數量: ${finalStats.items_count}`);
        console.log(`👥 學生分配數量: ${finalStats.assignments_count}`);
        
        console.log('🎉 重複數據清理完成！');
        
    } catch (error) {
        console.error('❌ 清理失敗:', error);
        throw error;
    } finally {
        await database.close();
    }
}

if (require.main === module) {
    cleanupDuplicates()
        .then(() => {
            console.log('✅ 清理腳本執行完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ 清理腳本執行失敗:', error);
            process.exit(1);
        });
}

module.exports = { cleanupDuplicates };
