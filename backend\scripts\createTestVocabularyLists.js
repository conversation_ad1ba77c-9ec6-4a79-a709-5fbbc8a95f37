const database = require('../config/database');

async function createTestVocabularyLists() {
    console.log('🧪 創建測試詞彙列表...\n');
    
    try {
        await database.connect();
        
        // 獲取teacher01和student01的ID
        const teacher = await database.get('SELECT id FROM users WHERE username = ?', ['teacher01']);
        const student = await database.get('SELECT id FROM users WHERE username = ?', ['student01']);
        
        if (!teacher || !student) {
            throw new Error('找不到teacher01或student01用戶');
        }
        
        console.log(`👨‍🏫 教師ID: ${teacher.id}`);
        console.log(`👨‍🎓 學生ID: ${student.id}\n`);
        
        // 測試詞彙列表1：基礎動物詞彙
        const list1Data = {
            name: '基礎動物詞彙',
            description: '學習常見動物的英文名稱',
            grade: 'F1',
            subject: 'English',
            vocabularies: [
                {
                    text: 'cat',
                    phonetic: '/kæt/',
                    part_of_speech: 'noun',
                    definition_en: 'A small domesticated carnivorous mammal',
                    translation_zh_tw: '貓',
                    sentence_en: 'The cat is sleeping on the sofa.',
                    image_path: 'images/cat.png'
                },
                {
                    text: 'dog',
                    phonetic: '/dɔːɡ/',
                    part_of_speech: 'noun',
                    definition_en: 'A domesticated carnivorous mammal',
                    translation_zh_tw: '狗',
                    sentence_en: 'My dog loves to play in the park.',
                    image_path: 'images/dog.png'
                },
                {
                    text: 'bird',
                    phonetic: '/bɜːrd/',
                    part_of_speech: 'noun',
                    definition_en: 'A warm-blooded vertebrate animal with feathers',
                    translation_zh_tw: '鳥',
                    sentence_en: 'The bird is singing in the tree.',
                    image_path: 'images/bird.png'
                },
                {
                    text: 'fish',
                    phonetic: '/fɪʃ/',
                    part_of_speech: 'noun',
                    definition_en: 'A limbless cold-blooded vertebrate animal with gills',
                    translation_zh_tw: '魚',
                    sentence_en: 'We saw many colorful fish in the aquarium.',
                    image_path: 'images/fish.png'
                },
                {
                    text: 'rabbit',
                    phonetic: '/ˈræbɪt/',
                    part_of_speech: 'noun',
                    definition_en: 'A small mammal with long ears and a short tail',
                    translation_zh_tw: '兔子',
                    sentence_en: 'The rabbit is eating carrots.',
                    image_path: 'images/rabbit.png'
                }
            ]
        };
        
        // 測試詞彙列表2：日常食物詞彙
        const list2Data = {
            name: '日常食物詞彙',
            description: '學習常見食物的英文名稱',
            grade: 'F1',
            subject: 'English',
            vocabularies: [
                {
                    text: 'apple',
                    phonetic: '/ˈæpəl/',
                    part_of_speech: 'noun',
                    definition_en: 'A round fruit with red or green skin',
                    translation_zh_tw: '蘋果',
                    sentence_en: 'I eat an apple every day.',
                    image_path: 'images/apple.png'
                },
                {
                    text: 'bread',
                    phonetic: '/bred/',
                    part_of_speech: 'noun',
                    definition_en: 'Food made of flour, water, and yeast',
                    translation_zh_tw: '麵包',
                    sentence_en: 'She bought fresh bread from the bakery.',
                    image_path: 'images/bread.png'
                },
                {
                    text: 'milk',
                    phonetic: '/mɪlk/',
                    part_of_speech: 'noun',
                    definition_en: 'A white liquid produced by mammals',
                    translation_zh_tw: '牛奶',
                    sentence_en: 'Children need to drink milk for strong bones.',
                    image_path: 'images/milk.png'
                },
                {
                    text: 'rice',
                    phonetic: '/raɪs/',
                    part_of_speech: 'noun',
                    definition_en: 'A cereal grain that is a staple food',
                    translation_zh_tw: '米飯',
                    sentence_en: 'We eat rice with chopsticks.',
                    image_path: 'images/rice.png'
                },
                {
                    text: 'egg',
                    phonetic: '/eɡ/',
                    part_of_speech: 'noun',
                    definition_en: 'An oval object laid by female birds',
                    translation_zh_tw: '雞蛋',
                    sentence_en: 'I had scrambled eggs for breakfast.',
                    image_path: 'images/egg.png'
                }
            ]
        };
        
        // 創建詞彙列表函數
        async function createVocabularyList(listData, teacherId) {
            console.log(`📝 創建詞彙列表: ${listData.name}`);
            
            // 創建詞彙列表
            const listResult = await database.run(`
                INSERT INTO vocabulary_lists (name, description, grade, subject, total_words, created_by, created_at)
                VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
            `, [listData.name, listData.description, listData.grade, listData.subject, listData.vocabularies.length, teacherId]);

            const listId = listResult.id;
            console.log(`   ✅ 列表ID: ${listId}`);

            if (!listId) {
                throw new Error('創建詞彙列表失敗，無法獲取列表ID');
            }
            
            // 創建詞彙並添加到列表
            for (let i = 0; i < listData.vocabularies.length; i++) {
                const vocab = listData.vocabularies[i];
                
                // 檢查詞彙是否已存在
                let vocabularyId;
                const existingVocab = await database.get(`
                    SELECT id FROM vocabularies WHERE text = ?
                `, [vocab.text]);

                if (existingVocab) {
                    vocabularyId = existingVocab.id;
                    console.log(`   📖 使用現有詞彙: ${vocab.text} (ID: ${vocabularyId})`);

                    // 更新現有詞彙的信息
                    await database.run(`
                        UPDATE vocabularies SET
                            phonetic = ?, part_of_speech = ?, definition_en = ?,
                            translation_zh_tw = ?, sentence_en = ?, image_path = ?
                        WHERE id = ?
                    `, [
                        vocab.phonetic, vocab.part_of_speech, vocab.definition_en,
                        vocab.translation_zh_tw, vocab.sentence_en, vocab.image_path, vocabularyId
                    ]);
                } else {
                    // 創建新詞彙 (生成word_id)
                    const wordId = `word_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                    const vocabResult = await database.run(`
                        INSERT INTO vocabularies (
                            word_id, text, phonetic, part_of_speech, definition_en,
                            translation_zh_tw, sentence_en, image_path
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    `, [
                        wordId, vocab.text, vocab.phonetic, vocab.part_of_speech, vocab.definition_en,
                        vocab.translation_zh_tw, vocab.sentence_en, vocab.image_path
                    ]);
                    vocabularyId = vocabResult.id;
                    console.log(`   📖 創建新詞彙: ${vocab.text} (ID: ${vocabularyId})`);
                }

                if (!vocabularyId) {
                    throw new Error(`無法獲取詞彙ID: ${vocab.text}`);
                }

                // 添加到詞彙列表
                await database.run(`
                    INSERT INTO vocabulary_list_items (list_id, vocabulary_id, order_index)
                    VALUES (?, ?, ?)
                `, [listId, vocabularyId, i]);

                console.log(`   ➕ 已添加到列表: ${vocab.text}`);
            }
            
            return listId;
        }
        
        // 創建兩個詞彙列表
        console.log('1️⃣ 創建第一個詞彙列表...');
        const list1Id = await createVocabularyList(list1Data, teacher.id);
        
        console.log('\n2️⃣ 創建第二個詞彙列表...');
        const list2Id = await createVocabularyList(list2Data, teacher.id);
        
        // 分配詞彙列表給student01
        console.log('\n👨‍🎓 分配詞彙列表給student01...');
        
        // 分配列表1
        await database.run(`
            INSERT INTO user_vocabulary_lists (user_id, list_id, assigned_by, assigned_at, status, progress_percentage)
            VALUES (?, ?, ?, datetime('now'), 'active', 0)
        `, [student.id, list1Id, teacher.id]);
        console.log(`   ✅ 已分配: ${list1Data.name}`);
        
        // 分配列表2
        await database.run(`
            INSERT INTO user_vocabulary_lists (user_id, list_id, assigned_by, assigned_at, status, progress_percentage)
            VALUES (?, ?, ?, datetime('now'), 'active', 0)
        `, [student.id, list2Id, teacher.id]);
        console.log(`   ✅ 已分配: ${list2Data.name}`);
        
        // 驗證創建結果
        console.log('\n📊 驗證創建結果...');
        
        const lists = await database.all(`
            SELECT id, name, total_words FROM vocabulary_lists 
            WHERE created_by = ? ORDER BY id
        `, [teacher.id]);
        
        console.log('   創建的詞彙列表:');
        lists.forEach(list => {
            console.log(`   - [${list.id}] ${list.name} (${list.total_words}個詞彙)`);
        });
        
        const assignments = await database.all(`
            SELECT vl.name, uvl.status, uvl.progress_percentage
            FROM user_vocabulary_lists uvl
            JOIN vocabulary_lists vl ON uvl.list_id = vl.id
            WHERE uvl.user_id = ?
        `, [student.id]);
        
        console.log('\n   student01的詞彙列表分配:');
        assignments.forEach(assignment => {
            console.log(`   - ${assignment.name} [${assignment.status}, ${assignment.progress_percentage}%]`);
        });
        
        console.log('\n🎉 測試詞彙列表創建完成！');
        console.log('\n📝 測試建議:');
        console.log('1. 使用student01登入系統');
        console.log('2. 進入詞彙學習頁面');
        console.log('3. 應該能看到2個詞彙列表可供選擇');
        console.log('4. 點擊任一列表進入詞彙學習');
        console.log('5. 測試練習功能是否正常');
        
    } catch (error) {
        console.error('❌ 創建測試詞彙列表失敗:', error);
    } finally {
        await database.close();
    }
}

// 如果直接運行此腳本
if (require.main === module) {
    createTestVocabularyLists()
        .then(() => {
            console.log('\n✅ 腳本執行完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('❌ 腳本執行失敗:', error);
            process.exit(1);
        });
}

module.exports = { createTestVocabularyLists };
