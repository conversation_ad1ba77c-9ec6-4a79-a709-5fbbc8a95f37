const fs = require('fs');
const path = require('path');
const database = require('../config/database');

async function migrateExistingData() {
    try {
        console.log('🚀 開始詞彙列表管理系統數據遷移...');

        // 確保數據庫連接
        if (!database.isConnected()) {
            await database.connect();
        }

        // 1. 執行數據庫架構更新
        console.log('📊 執行數據庫架構更新...');
        const migrationSQL = fs.readFileSync(
            path.join(__dirname, '../database/migrations/add_vocabulary_lists.sql'),
            'utf8'
        );

        // 分割SQL語句並逐個執行
        const statements = migrationSQL.split(';').filter(stmt => stmt.trim());
        for (const statement of statements) {
            if (statement.trim()) {
                await database.run(statement.trim());
            }
        }
        console.log('✅ 數據庫架構更新完成');
        
        // 2. 獲取現有詞彙數據
        console.log('📋 分析現有詞彙數據...');
        const vocabularies = await database.all('SELECT * FROM vocabularies ORDER BY id');
        console.log(`📝 找到 ${vocabularies.length} 個詞彙`);
        
        // 3. 獲取現有用戶數據
        const students = await database.all(`
            SELECT id, username, grade FROM users 
            WHERE role = 'student' 
            ORDER BY grade, id
        `);
        console.log(`👥 找到 ${students.length} 個學生`);
        
        // 4. 獲取已創建的詞彙列表
        const lists = await database.all('SELECT * FROM vocabulary_lists ORDER BY id');
        console.log(`📚 創建了 ${lists.length} 個詞彙列表`);
        
        // 5. 為每個詞彙列表添加詞彙內容
        for (const list of lists) {
            console.log(`🔧 處理列表: ${list.name}`);
            
            // 根據年級篩選詞彙（如果有年級信息）
            let listVocabularies = vocabularies;
            if (list.grade && list.grade !== 'ALL') {
                // 這裡可以根據實際需求調整詞彙篩選邏輯
                // 目前將所有詞彙分配給所有列表，實際使用時可以根據需求調整
                listVocabularies = vocabularies.slice(0, Math.min(5, vocabularies.length));
            }
            
            // 添加詞彙到列表
            for (let i = 0; i < listVocabularies.length; i++) {
                const vocab = listVocabularies[i];
                try {
                    await database.run(`
                        INSERT INTO vocabulary_list_items (list_id, vocabulary_id, order_index)
                        VALUES (?, ?, ?)
                    `, [list.id, vocab.id, i + 1]);
                } catch (error) {
                    // 忽略重複插入錯誤
                    if (!error.message.includes('UNIQUE constraint failed')) {
                        throw error;
                    }
                }
            }
            
            // 更新詞彙總數
            const itemCount = await database.get(`
                SELECT COUNT(*) as count 
                FROM vocabulary_list_items 
                WHERE list_id = ?
            `, [list.id]);
            
            await database.run(`
                UPDATE vocabulary_lists 
                SET total_words = ? 
                WHERE id = ?
            `, [itemCount.count, list.id]);
            
            console.log(`✅ 列表 "${list.name}" 添加了 ${itemCount.count} 個詞彙`);
        }
        
        // 6. 為學生分配詞彙列表
        console.log('👥 為學生分配詞彙列表...');
        for (const student of students) {
            // 根據學生年級分配對應的詞彙列表
            const studentLists = lists.filter(list => 
                list.grade === student.grade || list.grade === 'S1' // 默認分配S1列表
            );
            
            for (const list of studentLists) {
                try {
                    await database.run(`
                        INSERT INTO user_vocabulary_lists (user_id, list_id, assigned_by)
                        VALUES (?, ?, ?)
                    `, [student.id, list.id, 1]); // assigned_by = teacher01
                    
                    console.log(`📋 為學生 ${student.username} 分配列表 "${list.name}"`);
                } catch (error) {
                    // 忽略重複分配錯誤
                    if (!error.message.includes('UNIQUE constraint failed')) {
                        throw error;
                    }
                }
            }
        }
        
        // 7. 計算並更新學生的列表進度
        console.log('📊 計算學生學習進度...');
        const assignments = await database.all(`
            SELECT uvl.*, vl.name as list_name 
            FROM user_vocabulary_lists uvl
            JOIN vocabulary_lists vl ON uvl.list_id = vl.id
        `);
        
        for (const assignment of assignments) {
            // 獲取該學生在該列表中的詞彙學習進度
            const progressData = await database.get(`
                SELECT 
                    COUNT(*) as total_words,
                    SUM(CASE WHEN uv.status = 'mastered' THEN 1 ELSE 0 END) as mastered_words,
                    AVG(CASE WHEN uv.progress > 0 THEN uv.progress ELSE 0 END) as avg_progress
                FROM vocabulary_list_items vli
                JOIN user_vocabularies uv ON vli.vocabulary_id = uv.vocabulary_id
                WHERE vli.list_id = ? AND uv.user_id = ?
            `, [assignment.list_id, assignment.user_id]);
            
            let progressPercentage = 0;
            if (progressData && progressData.total_words > 0) {
                progressPercentage = Math.round(
                    (progressData.mastered_words * 100 + (progressData.avg_progress || 0)) / 
                    (progressData.total_words + 1)
                );
            }
            
            await database.run(`
                UPDATE user_vocabulary_lists 
                SET progress_percentage = ?
                WHERE id = ?
            `, [progressPercentage, assignment.id]);
        }
        
        console.log('🎉 數據遷移完成！');
        
        // 8. 顯示遷移結果統計
        const finalStats = await database.get(`
            SELECT 
                (SELECT COUNT(*) FROM vocabulary_lists) as lists_count,
                (SELECT COUNT(*) FROM vocabulary_list_items) as items_count,
                (SELECT COUNT(*) FROM user_vocabulary_lists) as assignments_count
        `);
        
        console.log('\n📊 遷移結果統計:');
        console.log(`📚 詞彙列表數量: ${finalStats.lists_count}`);
        console.log(`📝 列表項目數量: ${finalStats.items_count}`);
        console.log(`👥 學生分配數量: ${finalStats.assignments_count}`);
        
    } catch (error) {
        console.error('❌ 數據遷移失敗:', error);
        throw error;
    }
}

if (require.main === module) {
    migrateExistingData()
        .then(() => {
            console.log('✅ 遷移腳本執行完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ 遷移腳本執行失敗:', error);
            process.exit(1);
        });
}

module.exports = { migrateExistingData };
