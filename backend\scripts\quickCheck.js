const database = require('../config/database');

async function quickCheck() {
    try {
        await database.connect();
        
        const vocabs = await database.all('SELECT text, image_path FROM vocabularies WHERE text IN ("festival", "delicious", "adventure") ORDER BY text');
        
        console.log('📋 關鍵詞彙的圖片路徑:');
        vocabs.forEach(v => {
            console.log(`  ${v.text}: ${v.image_path}`);
        });
        
        await database.close();
    } catch (error) {
        console.error('錯誤:', error);
    }
}

// quickCheck(); // 註釋掉自動執行，避免在require時執行

// 只有直接運行此腳本時才執行
if (require.main === module) {
    quickCheck();
}
