const database = require('../config/database');

async function simpleMigration() {
    try {
        console.log('🚀 開始簡化版數據遷移...');
        
        // 連接數據庫
        await database.connect();
        console.log('✅ 數據庫連接成功');
        
        // 1. 創建詞彙列表主表
        console.log('📊 創建詞彙列表主表...');
        await database.run(`
            CREATE TABLE IF NOT EXISTS vocabulary_lists (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                grade TEXT,
                subject TEXT,
                total_words INTEGER DEFAULT 0,
                created_by INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
        `);
        console.log('✅ vocabulary_lists 表創建成功');
        
        // 2. 創建詞彙列表內容表
        console.log('📊 創建詞彙列表內容表...');
        await database.run(`
            CREATE TABLE IF NOT EXISTS vocabulary_list_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                list_id INTEGER NOT NULL,
                vocabulary_id INTEGER NOT NULL,
                order_index INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (list_id) REFERENCES vocabulary_lists(id) ON DELETE CASCADE,
                FOREIGN KEY (vocabulary_id) REFERENCES vocabularies(id) ON DELETE CASCADE,
                UNIQUE(list_id, vocabulary_id)
            )
        `);
        console.log('✅ vocabulary_list_items 表創建成功');
        
        // 3. 創建學生詞彙列表分配表
        console.log('📊 創建學生詞彙列表分配表...');
        await database.run(`
            CREATE TABLE IF NOT EXISTS user_vocabulary_lists (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                list_id INTEGER NOT NULL,
                assigned_by INTEGER NOT NULL,
                assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                due_date DATETIME,
                status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'archived')),
                progress_percentage INTEGER DEFAULT 0,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (list_id) REFERENCES vocabulary_lists(id) ON DELETE CASCADE,
                FOREIGN KEY (assigned_by) REFERENCES users(id),
                UNIQUE(user_id, list_id)
            )
        `);
        console.log('✅ user_vocabulary_lists 表創建成功');
        
        // 4. 創建索引
        console.log('📊 創建索引...');
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_vocabulary_lists_created_by ON vocabulary_lists(created_by)',
            'CREATE INDEX IF NOT EXISTS idx_vocabulary_lists_grade ON vocabulary_lists(grade)',
            'CREATE INDEX IF NOT EXISTS idx_vocabulary_list_items_list_id ON vocabulary_list_items(list_id)',
            'CREATE INDEX IF NOT EXISTS idx_vocabulary_list_items_order ON vocabulary_list_items(list_id, order_index)',
            'CREATE INDEX IF NOT EXISTS idx_user_vocabulary_lists_user_id ON user_vocabulary_lists(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_user_vocabulary_lists_status ON user_vocabulary_lists(user_id, status)'
        ];
        
        for (const indexSQL of indexes) {
            await database.run(indexSQL);
        }
        console.log('✅ 索引創建成功');
        
        // 5. 插入默認詞彙列表
        console.log('📊 插入默認詞彙列表...');
        const defaultLists = [
            ['中一基礎詞彙', '中一學生基礎詞彙學習', 'S1', '基礎詞彙', 1],
            ['中二進階詞彙', '中二學生進階詞彙學習', 'S2', '進階詞彙', 1],
            ['節日主題詞彙', '關於節日的詞彙學習', 'S1', '節日', 1]
        ];
        
        for (const [name, description, grade, subject, created_by] of defaultLists) {
            try {
                await database.run(`
                    INSERT INTO vocabulary_lists (name, description, grade, subject, created_by)
                    VALUES (?, ?, ?, ?, ?)
                `, [name, description, grade, subject, created_by]);
                console.log(`✅ 創建列表: ${name}`);
            } catch (error) {
                if (error.message.includes('UNIQUE constraint failed')) {
                    console.log(`⚠️ 列表已存在: ${name}`);
                } else {
                    throw error;
                }
            }
        }
        
        // 6. 獲取現有詞彙並分配到列表
        console.log('📊 分配詞彙到列表...');
        const vocabularies = await database.all('SELECT * FROM vocabularies ORDER BY id LIMIT 5');
        const lists = await database.all('SELECT * FROM vocabulary_lists');
        
        for (const list of lists) {
            for (let i = 0; i < vocabularies.length; i++) {
                const vocab = vocabularies[i];
                try {
                    await database.run(`
                        INSERT INTO vocabulary_list_items (list_id, vocabulary_id, order_index)
                        VALUES (?, ?, ?)
                    `, [list.id, vocab.id, i + 1]);
                } catch (error) {
                    if (!error.message.includes('UNIQUE constraint failed')) {
                        throw error;
                    }
                }
            }
            
            // 更新詞彙總數
            await database.run(`
                UPDATE vocabulary_lists 
                SET total_words = ? 
                WHERE id = ?
            `, [vocabularies.length, list.id]);
            
            console.log(`✅ 列表 "${list.name}" 添加了 ${vocabularies.length} 個詞彙`);
        }
        
        // 7. 為學生分配列表
        console.log('📊 為學生分配列表...');
        const students = await database.all(`
            SELECT id, username, grade FROM users 
            WHERE role = 'student' 
            ORDER BY id LIMIT 5
        `);
        
        for (const student of students) {
            for (const list of lists) {
                try {
                    await database.run(`
                        INSERT INTO user_vocabulary_lists (user_id, list_id, assigned_by)
                        VALUES (?, ?, ?)
                    `, [student.id, list.id, 1]);
                    console.log(`✅ 為學生 ${student.username} 分配列表 "${list.name}"`);
                } catch (error) {
                    if (!error.message.includes('UNIQUE constraint failed')) {
                        throw error;
                    }
                }
            }
        }
        
        console.log('🎉 數據遷移完成！');
        
        // 顯示統計信息
        const stats = await database.get(`
            SELECT 
                (SELECT COUNT(*) FROM vocabulary_lists) as lists_count,
                (SELECT COUNT(*) FROM vocabulary_list_items) as items_count,
                (SELECT COUNT(*) FROM user_vocabulary_lists) as assignments_count
        `);
        
        console.log('\n📊 遷移結果統計:');
        console.log(`📚 詞彙列表數量: ${stats.lists_count}`);
        console.log(`📝 列表項目數量: ${stats.items_count}`);
        console.log(`👥 學生分配數量: ${stats.assignments_count}`);
        
    } catch (error) {
        console.error('❌ 遷移失敗:', error);
        throw error;
    } finally {
        await database.close();
    }
}

if (require.main === module) {
    simpleMigration()
        .then(() => {
            console.log('✅ 遷移腳本執行完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ 遷移腳本執行失敗:', error);
            process.exit(1);
        });
}

module.exports = { simpleMigration };
