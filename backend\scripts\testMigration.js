const database = require('../config/database');

async function testMigration() {
    try {
        console.log('🔍 測試數據庫連接和遷移...');
        
        // 連接數據庫
        await database.connect();
        console.log('✅ 數據庫連接成功');
        
        // 測試簡單查詢
        const users = await database.all('SELECT COUNT(*) as count FROM users');
        console.log(`👥 用戶數量: ${users[0].count}`);
        
        // 測試創建表
        console.log('📊 創建測試表...');
        await database.run(`
            CREATE TABLE IF NOT EXISTS test_vocabulary_lists (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);
        console.log('✅ 測試表創建成功');
        
        // 插入測試數據
        await database.run(`
            INSERT INTO test_vocabulary_lists (name) VALUES (?)
        `, ['測試列表']);
        console.log('✅ 測試數據插入成功');
        
        // 查詢測試數據
        const testData = await database.all('SELECT * FROM test_vocabulary_lists');
        console.log('📋 測試數據:', testData);
        
        // 清理測試表
        await database.run('DROP TABLE test_vocabulary_lists');
        console.log('✅ 測試表清理完成');
        
        console.log('🎉 測試完成，數據庫工作正常');
        
    } catch (error) {
        console.error('❌ 測試失敗:', error);
    } finally {
        await database.close();
    }
}

testMigration();
