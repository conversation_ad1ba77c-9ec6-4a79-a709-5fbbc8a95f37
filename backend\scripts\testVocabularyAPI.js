const database = require('../config/database');

async function testVocabularyAPI() {
    try {
        await database.connect();
        
        console.log('🧪 測試詞彙API查詢...\n');
        
        const userId = 4; // student01
        const listId = 9; // 基礎動物詞彙
        
        // 測試權限檢查查詢
        console.log('1️⃣ 測試權限檢查...');
        const assignment = await database.get(`
            SELECT uvl.*, vl.name, vl.description, vl.grade, vl.subject
            FROM user_vocabulary_lists uvl
            JOIN vocabulary_lists vl ON uvl.list_id = vl.id
            WHERE uvl.user_id = ? AND uvl.list_id = ? AND uvl.status = 'active'
        `, [userId, listId]);
        
        if (assignment) {
            console.log('✅ 權限檢查通過:', assignment.name);
        } else {
            console.log('❌ 權限檢查失敗');
            return;
        }
        
        // 測試詞彙查詢（修復後的版本）
        console.log('\n2️⃣ 測試詞彙查詢...');
        const vocabularies = await database.all(`
            SELECT 
                v.id,
                v.text,
                v.phonetic,
                v.part_of_speech,
                v.translation_zh_tw,
                v.definition_en,
                v.sentence_en,
                v.image_path,
                uv.status,
                uv.progress,
                uv.practice_count,
                uv.correct_count,
                vli.order_index
            FROM vocabulary_list_items vli
            JOIN vocabularies v ON vli.vocabulary_id = v.id
            LEFT JOIN user_vocabularies uv ON v.id = uv.vocabulary_id AND uv.user_id = ?
            WHERE vli.list_id = ?
            ORDER BY vli.order_index ASC
        `, [userId, listId]);
        
        console.log(`✅ 找到 ${vocabularies.length} 個詞彙:`);
        vocabularies.forEach((vocab, index) => {
            console.log(`   ${index + 1}. ${vocab.text} - ${vocab.translation_zh_tw}`);
            console.log(`      音標: ${vocab.phonetic || '無'}`);
            console.log(`      狀態: ${vocab.status || '未開始'}`);
            console.log(`      進度: ${vocab.progress || 0}%`);
        });
        
        // 計算統計信息
        const stats = {
            total: vocabularies.length,
            mastered: vocabularies.filter(v => v.status === 'mastered').length,
            learning: vocabularies.filter(v => v.status === 'learning').length,
            assigned: vocabularies.filter(v => v.status === 'assigned' || !v.status).length
        };
        
        console.log('\n📊 統計信息:');
        console.log(`   總詞彙數: ${stats.total}`);
        console.log(`   已掌握: ${stats.mastered}`);
        console.log(`   學習中: ${stats.learning}`);
        console.log(`   已分配: ${stats.assigned}`);
        
        console.log('\n✅ API查詢測試成功！');
        
    } catch (error) {
        console.error('❌ 測試失敗:', error);
    } finally {
        await database.close();
    }
}

if (require.main === module) {
    testVocabularyAPI();
}

module.exports = { testVocabularyAPI };
