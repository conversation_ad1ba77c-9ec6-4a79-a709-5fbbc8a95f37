const database = require('../config/database');

async function testVocabularyListSystem() {
    console.log('🧪 測試詞彙列表系統...\n');
    
    try {
        // 1. 測試詞彙列表表結構
        console.log('1. 檢查詞彙列表表結構...');
        
        const tables = await database.all(`
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name LIKE '%vocabulary_list%'
            ORDER BY name
        `);
        
        console.log('   詞彙列表相關表:', tables.map(t => t.name).join(', '));
        
        // 2. 測試詞彙列表數據
        console.log('\n2. 檢查詞彙列表數據...');
        
        const lists = await database.all(`
            SELECT id, name, description, grade, subject, total_words, created_by
            FROM vocabulary_lists
            ORDER BY id
        `);
        
        console.log(`   找到 ${lists.length} 個詞彙列表:`);
        lists.forEach(list => {
            console.log(`   - [${list.id}] ${list.name} (${list.grade}-${list.subject}, ${list.total_words}詞)`);
        });
        
        // 3. 測試詞彙列表項目
        console.log('\n3. 檢查詞彙列表項目...');
        
        const listItems = await database.all(`
            SELECT 
                vli.list_id,
                vl.name as list_name,
                COUNT(vli.vocabulary_id) as item_count
            FROM vocabulary_list_items vli
            JOIN vocabulary_lists vl ON vli.list_id = vl.id
            GROUP BY vli.list_id, vl.name
            ORDER BY vli.list_id
        `);
        
        console.log(`   詞彙列表項目統計:`);
        listItems.forEach(item => {
            console.log(`   - 列表 [${item.list_id}] ${item.list_name}: ${item.item_count} 個詞彙`);
        });
        
        // 4. 測試學生分配
        console.log('\n4. 檢查學生分配...');
        
        const assignments = await database.all(`
            SELECT 
                uvl.list_id,
                vl.name as list_name,
                COUNT(uvl.user_id) as assigned_students
            FROM user_vocabulary_lists uvl
            JOIN vocabulary_lists vl ON uvl.list_id = vl.id
            GROUP BY uvl.list_id, vl.name
            ORDER BY uvl.list_id
        `);
        
        console.log(`   學生分配統計:`);
        assignments.forEach(assignment => {
            console.log(`   - 列表 [${assignment.list_id}] ${assignment.list_name}: ${assignment.assigned_students} 個學生`);
        });
        
        // 5. 測試學生詞彙列表視圖
        console.log('\n5. 測試學生詞彙列表視圖...');
        
        const studentLists = await database.all(`
            SELECT 
                u.username,
                u.full_name,
                vl.name as list_name,
                uvl.status,
                uvl.progress_percentage
            FROM user_vocabulary_lists uvl
            JOIN users u ON uvl.user_id = u.id
            JOIN vocabulary_lists vl ON uvl.list_id = vl.id
            WHERE u.role = 'student'
            ORDER BY u.username, vl.name
            LIMIT 10
        `);
        
        console.log(`   學生詞彙列表分配 (前10條):`);
        studentLists.forEach(item => {
            console.log(`   - ${item.username} (${item.full_name}): ${item.list_name} [${item.status}, ${item.progress_percentage}%]`);
        });
        
        // 6. 測試詞彙列表詞彙查詢
        console.log('\n6. 測試詞彙列表詞彙查詢...');
        
        if (lists.length > 0) {
            const firstListId = lists[0].id;
            const vocabularies = await database.all(`
                SELECT 
                    v.text,
                    v.translation_zh_tw,
                    vli.order_index
                FROM vocabulary_list_items vli
                JOIN vocabularies v ON vli.vocabulary_id = v.id
                WHERE vli.list_id = ?
                ORDER BY vli.order_index
                LIMIT 5
            `, [firstListId]);
            
            console.log(`   列表 [${firstListId}] ${lists[0].name} 的詞彙 (前5個):`);
            vocabularies.forEach(vocab => {
                console.log(`   - [${vocab.order_index}] ${vocab.text} - ${vocab.translation_zh_tw}`);
            });
        }
        
        // 7. 測試API端點數據結構
        console.log('\n7. 測試API數據結構...');
        
        // 模擬學生API查詢
        const studentId = 1; // 假設student01的ID是1
        const studentVocabLists = await database.all(`
            SELECT 
                vl.id,
                vl.name,
                vl.description,
                vl.grade,
                vl.subject,
                vl.total_words,
                uvl.assigned_at,
                uvl.due_date,
                uvl.status,
                uvl.progress_percentage
            FROM user_vocabulary_lists uvl
            JOIN vocabulary_lists vl ON uvl.list_id = vl.id
            WHERE uvl.user_id = ? AND uvl.status = 'active'
            ORDER BY uvl.assigned_at DESC
        `, [studentId]);
        
        console.log(`   學生 [${studentId}] 的詞彙列表:`);
        studentVocabLists.forEach(list => {
            console.log(`   - ${list.name}: ${list.total_words}詞, 進度${list.progress_percentage}%`);
        });
        
        // 8. 系統完整性檢查
        console.log('\n8. 系統完整性檢查...');
        
        const integrityChecks = await Promise.all([
            // 檢查孤立的詞彙列表項目
            database.get(`
                SELECT COUNT(*) as count 
                FROM vocabulary_list_items vli
                LEFT JOIN vocabulary_lists vl ON vli.list_id = vl.id
                WHERE vl.id IS NULL
            `),
            // 檢查孤立的學生分配
            database.get(`
                SELECT COUNT(*) as count 
                FROM user_vocabulary_lists uvl
                LEFT JOIN vocabulary_lists vl ON uvl.list_id = vl.id
                WHERE vl.id IS NULL
            `),
            // 檢查詞彙總數一致性
            database.all(`
                SELECT 
                    vl.id,
                    vl.name,
                    vl.total_words as recorded_count,
                    COUNT(vli.vocabulary_id) as actual_count
                FROM vocabulary_lists vl
                LEFT JOIN vocabulary_list_items vli ON vl.id = vli.list_id
                GROUP BY vl.id, vl.name, vl.total_words
                HAVING recorded_count != actual_count
            `)
        ]);
        
        console.log(`   - 孤立的詞彙列表項目: ${integrityChecks[0].count}`);
        console.log(`   - 孤立的學生分配: ${integrityChecks[1].count}`);
        console.log(`   - 詞彙總數不一致的列表: ${integrityChecks[2].length}`);
        
        if (integrityChecks[2].length > 0) {
            console.log('   詞彙總數不一致的列表詳情:');
            integrityChecks[2].forEach(list => {
                console.log(`     - [${list.id}] ${list.name}: 記錄${list.recorded_count}, 實際${list.actual_count}`);
            });
        }
        
        console.log('\n✅ 詞彙列表系統測試完成!');
        
        // 總結
        console.log('\n📊 系統狀態總結:');
        console.log(`   - 詞彙列表總數: ${lists.length}`);
        console.log(`   - 詞彙列表項目總數: ${listItems.reduce((sum, item) => sum + item.item_count, 0)}`);
        console.log(`   - 學生分配總數: ${assignments.reduce((sum, assignment) => sum + assignment.assigned_students, 0)}`);
        console.log(`   - 數據完整性: ${integrityChecks[0].count === 0 && integrityChecks[1].count === 0 && integrityChecks[2].length === 0 ? '✅ 良好' : '⚠️ 需要注意'}`);
        
    } catch (error) {
        console.error('❌ 測試過程中發生錯誤:', error);
    }
}

// 如果直接運行此腳本
if (require.main === module) {
    testVocabularyListSystem()
        .then(() => {
            console.log('\n🎉 測試腳本執行完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('❌ 測試腳本執行失敗:', error);
            process.exit(1);
        });
}

module.exports = { testVocabularyListSystem };
