const database = require('../config/database');
const jwt = require('jsonwebtoken');

async function testVocabularyListsAPI() {
    console.log('🧪 測試詞彙列表API...\n');
    
    try {
        await database.connect();
        
        // 獲取student01用戶信息
        const student = await database.get('SELECT * FROM users WHERE username = ?', ['student01']);
        if (!student) {
            throw new Error('找不到student01用戶');
        }
        
        console.log('👨‍🎓 學生信息:', {
            id: student.id,
            username: student.username,
            role: student.role
        });
        
        // 生成有效的JWT token
        const token = jwt.sign(
            { 
                id: student.id, 
                username: student.username, 
                role: student.role 
            },
            process.env.JWT_SECRET || 'your-secret-key',
            { expiresIn: '24h' }
        );
        
        console.log('🔑 生成的JWT token:', token.substring(0, 50) + '...\n');
        
        // 測試API端點
        console.log('📡 測試API端點...');
        
        // 模擬API調用
        const mockReq = {
            user: {
                id: student.id,
                username: student.username,
                role: student.role
            }
        };
        
        // 直接調用數據庫查詢（模擬API邏輯）
        const lists = await database.all(`
            SELECT 
                vl.id,
                vl.name,
                vl.description,
                vl.grade,
                vl.subject,
                vl.total_words,
                uvl.assigned_at,
                uvl.due_date,
                uvl.status,
                uvl.progress_percentage
            FROM user_vocabulary_lists uvl
            JOIN vocabulary_lists vl ON uvl.list_id = vl.id
            WHERE uvl.user_id = ? AND uvl.status = 'active'
            ORDER BY uvl.assigned_at DESC
        `, [student.id]);
        
        console.log(`✅ 找到 ${lists.length} 個詞彙列表:`);
        lists.forEach((list, index) => {
            console.log(`   ${index + 1}. [${list.id}] ${list.name}`);
            console.log(`      - 描述: ${list.description || '無'}`);
            console.log(`      - 年級/科目: ${list.grade}/${list.subject}`);
            console.log(`      - 詞彙數量: ${list.total_words}`);
            console.log(`      - 進度: ${list.progress_percentage}%`);
            console.log(`      - 狀態: ${list.status}`);
            console.log('');
        });
        
        // 測試特定列表的詞彙
        if (lists.length > 0) {
            const firstList = lists[0];
            console.log(`📖 測試列表 "${firstList.name}" 的詞彙...`);
            
            const vocabularies = await database.all(`
                SELECT 
                    v.id,
                    v.text,
                    v.phonetic,
                    v.part_of_speech,
                    v.definition_en,
                    v.translation_zh_tw,
                    v.sentence_en,
                    v.image_path,
                    vli.order_index,
                    uv.status as learning_status,
                    uv.progress
                FROM vocabulary_list_items vli
                JOIN vocabularies v ON vli.vocabulary_id = v.id
                LEFT JOIN user_vocabularies uv ON v.id = uv.vocabulary_id AND uv.user_id = ?
                WHERE vli.list_id = ?
                ORDER BY vli.order_index
            `, [student.id, firstList.id]);
            
            console.log(`   找到 ${vocabularies.length} 個詞彙:`);
            vocabularies.forEach((vocab, index) => {
                console.log(`   ${index + 1}. ${vocab.text} - ${vocab.translation_zh_tw}`);
                console.log(`      音標: ${vocab.phonetic || '無'}`);
                console.log(`      學習狀態: ${vocab.learning_status || '未開始'}`);
            });
        }
        
        // 檢查API路由是否正確註冊
        console.log('\n🔍 檢查API路由配置...');
        
        // 檢查server.js中的路由配置
        const fs = require('fs');
        const path = require('path');
        const serverFile = fs.readFileSync(path.join(__dirname, '../server.js'), 'utf8');
        
        if (serverFile.includes("app.use('/api', require('./routes/vocabulary-lists'))")) {
            console.log('✅ vocabulary-lists路由已正確註冊');
        } else {
            console.log('❌ vocabulary-lists路由未正確註冊');
        }
        
        // 檢查路由文件是否存在
        const routeFile = path.join(__dirname, '../routes/vocabulary-lists.js');
        if (fs.existsSync(routeFile)) {
            console.log('✅ vocabulary-lists.js路由文件存在');
            
            const routeContent = fs.readFileSync(routeFile, 'utf8');
            if (routeContent.includes("router.get('/student/vocabulary-lists'")) {
                console.log('✅ 學生詞彙列表API端點存在');
            } else {
                console.log('❌ 學生詞彙列表API端點不存在');
            }
        } else {
            console.log('❌ vocabulary-lists.js路由文件不存在');
        }
        
        console.log('\n🎯 測試結論:');
        if (lists.length > 0) {
            console.log('✅ 數據庫中有詞彙列表數據');
            console.log('✅ API邏輯應該正常工作');
            console.log('⚠️  如果前端仍然顯示"暫無詞彙列表"，可能的原因:');
            console.log('   1. JWT token無效或過期');
            console.log('   2. 前端API調用有問題');
            console.log('   3. 網絡請求被阻擋');
            console.log('   4. 瀏覽器緩存問題');
        } else {
            console.log('❌ 數據庫中沒有詞彙列表數據');
        }
        
        console.log('\n📝 建議的調試步驟:');
        console.log('1. 檢查瀏覽器開發者工具的Network標籤');
        console.log('2. 查看API請求是否發送成功');
        console.log('3. 檢查API響應內容');
        console.log('4. 驗證JWT token是否有效');
        console.log('5. 嘗試重新登入獲取新token');
        
    } catch (error) {
        console.error('❌ 測試失敗:', error);
    } finally {
        await database.close();
    }
}

// 如果直接運行此腳本
if (require.main === module) {
    testVocabularyListsAPI()
        .then(() => {
            console.log('\n✅ 測試完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('❌ 測試失敗:', error);
            process.exit(1);
        });
}

module.exports = { testVocabularyListsAPI };
