const database = require('../config/database');

async function verifyMigration() {
    try {
        console.log('🔍 開始驗證數據遷移結果...\n');

        // 確保數據庫連接
        if (!database.isConnected()) {
            await database.connect();
        }
        
        // 1. 檢查新表是否創建成功
        console.log('📋 檢查數據庫表結構...');
        const tables = await database.all(`
            SELECT name FROM sqlite_master
            WHERE type='table' AND (name LIKE 'vocabulary_%' OR name = 'user_vocabulary_lists')
            ORDER BY name
        `);
        
        console.log('數據庫表:');
        tables.forEach(table => {
            console.log(`  ✅ ${table.name}`);
        });
        
        // 2. 檢查索引是否創建成功
        console.log('\n📊 檢查數據庫索引...');
        const indexes = await database.all(`
            SELECT name FROM sqlite_master 
            WHERE type='index' AND name LIKE 'idx_%'
            ORDER BY name
        `);
        
        console.log('數據庫索引:');
        indexes.forEach(index => {
            console.log(`  ✅ ${index.name}`);
        });
        
        // 3. 檢查數據遷移結果
        console.log('\n📊 檢查數據遷移結果...');
        const listsCount = await database.get('SELECT COUNT(*) as count FROM vocabulary_lists');
        const itemsCount = await database.get('SELECT COUNT(*) as count FROM vocabulary_list_items');
        const assignmentsCount = await database.get('SELECT COUNT(*) as count FROM user_vocabulary_lists');
        
        console.log(`📚 詞彙列表數量: ${listsCount.count}`);
        console.log(`📝 列表項目數量: ${itemsCount.count}`);
        console.log(`👥 學生分配數量: ${assignmentsCount.count}`);
        
        // 4. 檢查數據完整性
        console.log('\n🔍 檢查數據完整性...');
        const integrityCheck = await database.all(`
            SELECT 
                vl.id,
                vl.name,
                vl.grade,
                vl.subject,
                vl.total_words,
                COUNT(vli.id) as actual_words,
                COUNT(DISTINCT uvl.user_id) as assigned_students
            FROM vocabulary_lists vl
            LEFT JOIN vocabulary_list_items vli ON vl.id = vli.list_id
            LEFT JOIN user_vocabulary_lists uvl ON vl.id = uvl.list_id
            GROUP BY vl.id, vl.name, vl.grade, vl.subject, vl.total_words
            ORDER BY vl.id
        `);
        
        console.log('數據完整性檢查:');
        console.table(integrityCheck);
        
        // 5. 檢查學生分配情況
        console.log('\n👥 檢查學生分配情況...');
        const studentAssignments = await database.all(`
            SELECT 
                u.username,
                u.grade,
                COUNT(uvl.id) as assigned_lists,
                AVG(uvl.progress_percentage) as avg_progress
            FROM users u
            LEFT JOIN user_vocabulary_lists uvl ON u.id = uvl.user_id
            WHERE u.role = 'student'
            GROUP BY u.id, u.username, u.grade
            ORDER BY u.grade, u.username
        `);
        
        console.log('學生分配情況:');
        console.table(studentAssignments);
        
        // 6. 檢查外鍵約束
        console.log('\n🔗 檢查外鍵約束...');
        
        // 檢查vocabulary_lists表的外鍵
        const invalidCreators = await database.get(`
            SELECT COUNT(*) as count 
            FROM vocabulary_lists vl
            LEFT JOIN users u ON vl.created_by = u.id
            WHERE u.id IS NULL
        `);
        
        // 檢查vocabulary_list_items表的外鍵
        const invalidListItems = await database.get(`
            SELECT COUNT(*) as count 
            FROM vocabulary_list_items vli
            LEFT JOIN vocabulary_lists vl ON vli.list_id = vl.id
            LEFT JOIN vocabularies v ON vli.vocabulary_id = v.id
            WHERE vl.id IS NULL OR v.id IS NULL
        `);
        
        // 檢查user_vocabulary_lists表的外鍵
        const invalidAssignments = await database.get(`
            SELECT COUNT(*) as count 
            FROM user_vocabulary_lists uvl
            LEFT JOIN users u ON uvl.user_id = u.id
            LEFT JOIN vocabulary_lists vl ON uvl.list_id = vl.id
            LEFT JOIN users t ON uvl.assigned_by = t.id
            WHERE u.id IS NULL OR vl.id IS NULL OR t.id IS NULL
        `);
        
        console.log(`外鍵約束檢查:`);
        console.log(`  vocabulary_lists.created_by: ${invalidCreators.count === 0 ? '✅' : '❌'} (無效記錄: ${invalidCreators.count})`);
        console.log(`  vocabulary_list_items 外鍵: ${invalidListItems.count === 0 ? '✅' : '❌'} (無效記錄: ${invalidListItems.count})`);
        console.log(`  user_vocabulary_lists 外鍵: ${invalidAssignments.count === 0 ? '✅' : '❌'} (無效記錄: ${invalidAssignments.count})`);
        
        // 7. 檢查現有功能是否受影響
        console.log('\n🔄 檢查現有功能完整性...');
        
        const existingTables = await database.all(`
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name IN ('users', 'vocabularies', 'user_vocabularies', 'practice_records')
            ORDER BY name
        `);
        
        console.log('現有核心表:');
        existingTables.forEach(table => {
            console.log(`  ✅ ${table.name}`);
        });
        
        // 檢查現有數據數量
        const existingDataCheck = await database.get(`
            SELECT 
                (SELECT COUNT(*) FROM users) as users_count,
                (SELECT COUNT(*) FROM vocabularies) as vocabularies_count,
                (SELECT COUNT(*) FROM user_vocabularies) as user_vocabularies_count,
                (SELECT COUNT(*) FROM practice_records) as practice_records_count
        `);
        
        console.log('現有數據數量:');
        console.log(`  👥 用戶: ${existingDataCheck.users_count}`);
        console.log(`  📝 詞彙: ${existingDataCheck.vocabularies_count}`);
        console.log(`  📚 用戶詞彙: ${existingDataCheck.user_vocabularies_count}`);
        console.log(`  🎯 練習記錄: ${existingDataCheck.practice_records_count}`);
        
        // 8. 總結驗證結果
        console.log('\n🎉 驗證完成！');
        
        const allChecksPass = (
            tables.length >= 3 && // 至少3個新表
            indexes.length >= 6 && // 至少6個新索引
            listsCount.count > 0 && // 有詞彙列表
            itemsCount.count > 0 && // 有列表項目
            assignmentsCount.count > 0 && // 有學生分配
            invalidCreators.count === 0 && // 外鍵約束正確
            invalidListItems.count === 0 &&
            invalidAssignments.count === 0 &&
            existingTables.length === 4 // 現有表完整
        );
        
        if (allChecksPass) {
            console.log('✅ 所有驗證檢查通過，數據遷移成功！');
            return true;
        } else {
            console.log('❌ 部分驗證檢查失敗，請檢查遷移結果');
            console.log('檢查詳情:');
            console.log(`  新表數量: ${tables.length} (期望: >= 3)`);
            console.log(`  新索引數量: ${indexes.length} (期望: >= 6)`);
            console.log(`  詞彙列表數量: ${listsCount.count} (期望: > 0)`);
            console.log(`  列表項目數量: ${itemsCount.count} (期望: > 0)`);
            console.log(`  學生分配數量: ${assignmentsCount.count} (期望: > 0)`);
            console.log(`  外鍵約束檢查: ${invalidCreators.count === 0 && invalidListItems.count === 0 && invalidAssignments.count === 0}`);
            console.log(`  現有表完整性: ${existingTables.length} (期望: 4)`);
            return false;
        }
        
    } catch (error) {
        console.error('❌ 驗證過程中發生錯誤:', error);
        return false;
    }
}

if (require.main === module) {
    verifyMigration()
        .then((success) => {
            process.exit(success ? 0 : 1);
        })
        .catch(() => {
            process.exit(1);
        });
}

module.exports = { verifyMigration };
