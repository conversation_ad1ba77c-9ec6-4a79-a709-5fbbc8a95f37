const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const path = require('path');
const database = require('./config/database');

const app = express();
const PORT = process.env.PORT || 3000;

// 安全中間件
app.use(helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS 配置
app.use(cors({
    origin: process.env.NODE_ENV === 'production'
        ? ['https://your-domain.com']
        : ['http://localhost:5173', 'http://localhost:5174', 'http://127.0.0.1:5173', 'http://127.0.0.1:5174'],
    credentials: true
}));

// 請求限制
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分鐘
    max: 100, // 每個IP最多100個請求
    message: {
        success: false,
        message: '請求過於頻繁，請稍後再試'
    }
});
app.use('/api/', limiter);

// 日誌中間件
app.use(morgan('combined'));

// 解析JSON和URL編碼的請求體
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 靜態文件服務
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// 健康檢查端點
app.get('/health', async (req, res) => {
    try {
        const stats = await database.getStats();
        res.json({
            success: true,
            message: 'Server is running',
            timestamp: new Date().toISOString(),
            database: {
                connected: database.isConnected(),
                stats
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Health check failed',
            error: error.message
        });
    }
});

// API 路由
app.use('/api/auth', require('./routes/auth'));

// 學生功能 - 統一前綴
app.use('/api/student', require('./routes/students'));
app.use('/api/student', require('./routes/practice'));

// 教師功能 - 統一前綴
app.use('/api/teacher', require('./routes/vocabulary-lists'));
app.use('/api/teacher', require('./routes/student-progress'));

// 管理員功能 - 預留
// app.use('/api/admin', require('./routes/admin'));

// 404 處理
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: '找不到請求的資源'
    });
});

// 全局錯誤處理中間件
app.use((error, req, res, next) => {
    console.error('Global error handler:', error);
    
    // 處理不同類型的錯誤
    if (error.name === 'ValidationError') {
        return res.status(400).json({
            success: false,
            message: '請求數據驗證失敗',
            errors: error.details
        });
    }
    
    if (error.name === 'UnauthorizedError') {
        return res.status(401).json({
            success: false,
            message: '未授權的請求'
        });
    }
    
    // 默認錯誤響應
    res.status(500).json({
        success: false,
        message: process.env.NODE_ENV === 'production' 
            ? '服務器內部錯誤' 
            : error.message,
        ...(process.env.NODE_ENV !== 'production' && { stack: error.stack })
    });
});

// 優雅關閉處理
process.on('SIGTERM', async () => {
    console.log('SIGTERM received, shutting down gracefully');
    await database.close();
    process.exit(0);
});

process.on('SIGINT', async () => {
    console.log('SIGINT received, shutting down gracefully');
    await database.close();
    process.exit(0);
});

// 啟動服務器
async function startServer() {
    try {
        // 連接數據庫
        await database.connect();
        await database.initialize();
        
        // 啟動HTTP服務器
        app.listen(PORT, () => {
            console.log(`🚀 Server is running on port ${PORT}`);
            console.log(`📊 Health check: http://localhost:${PORT}/health`);
            console.log(`🔗 API base URL: http://localhost:${PORT}/api`);
        });
    } catch (error) {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
    }
}

startServer();
