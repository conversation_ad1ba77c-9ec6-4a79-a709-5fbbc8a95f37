const database = require('./config/database');

// 英文解釋映射
const definitions = {
    'apple': 'A round fruit with red, green, or yellow skin and white flesh',
    'book': 'A set of printed pages that are fastened inside a cover so that you can turn them and read them',
    'cat': 'A small animal with fur, four legs, a tail, and claws, usually kept as a pet',
    'dog': 'An animal with four legs and a tail, often kept as a pet or used for work or hunting',
    'elephant': 'A very large grey mammal that has a trunk and two long curved teeth called tusks',
    'fish': 'An animal that lives in water and uses its fins and tail to swim',
    'guitar': 'A musical instrument with six strings that you play by pulling the strings with your fingers',
    'house': 'A building that people live in, especially one that is intended for one family',
    'ice': 'Water that has frozen and become solid',
    'juice': 'The liquid that comes from fruit and vegetables'
};

async function updateDefinitions() {
    try {
        await database.connect();
        
        console.log('開始更新英文解釋...');
        
        let updatedCount = 0;
        
        for (const [word, definition] of Object.entries(definitions)) {
            try {
                const result = await database.run(`
                    UPDATE vocabularies 
                    SET definition_en = ? 
                    WHERE text = ? AND (definition_en IS NULL OR definition_en = '')
                `, [definition, word]);
                
                if (result.changes > 0) {
                    console.log(`✅ 更新了 "${word}" 的英文解釋`);
                    updatedCount += result.changes;
                } else {
                    console.log(`⚠️ 未找到詞彙 "${word}" 或已有英文解釋`);
                }
            } catch (error) {
                console.error(`❌ 更新 "${word}" 失敗:`, error.message);
            }
        }
        
        console.log(`\n總共更新了 ${updatedCount} 個詞彙的英文解釋`);
        
        // 驗證更新結果
        console.log('\n驗證更新結果:');
        const updatedVocabs = await database.all(`
            SELECT text, definition_en, translation_zh_tw
            FROM vocabularies 
            WHERE text IN (${Object.keys(definitions).map(() => '?').join(',')})
            ORDER BY text
        `, Object.keys(definitions));
        
        updatedVocabs.forEach(vocab => {
            console.log(`${vocab.text}: ${vocab.definition_en ? '✅' : '❌'} ${vocab.definition_en || '(仍為空)'}`);
        });
        
        await database.close();
    } catch (error) {
        console.error('更新失敗:', error);
    }
}

updateDefinitions();
