<template>
  <!-- 題目組件 - 基於參考設計的緊湊佈局 -->
  <div class="p-4 sm:p-6">
    <!-- 題目標題和指示 -->
    <div class="text-center mb-4">
      <p class="text-slate-600 mb-2 font-semibold">
        第 {{ questionNumber }} 題 - {{ question.type === 'multiple_choice' ? '選擇題' :
             question.type === 'fill_blank' ? '填空題' : '拼寫練習' }}
      </p>
      <div class="text-slate-800 mb-4 text-lg">{{ question.question }}</div>
    </div>

    <!-- 單詞信息 (如果有) -->
    <div v-if="question.word_info" class="text-center mb-4">
      <div class="text-2xl sm:text-3xl font-bold text-slate-900 mb-1">{{ question.word_info.text }}</div>
      <div v-if="question.word_info.phonetic" class="text-base sm:text-lg text-slate-500">{{ question.word_info.phonetic }}</div>
    </div>

    <!-- 選擇題 - 基於參考設計的緊湊佈局 -->
    <div v-if="question.type === 'multiple_choice'" class="space-y-3 mb-6">
      <button
        v-for="(option, index) in question.options"
        :key="index"
        @click="selectOption(option)"
        :disabled="answered"
        :class="[
          'w-full bg-white border border-slate-200 hover:bg-slate-100 text-slate-800 font-semibold py-3 px-6 rounded-lg shadow-sm transition-transform transform hover:scale-105',
          answered && option === question.correct_answer
            ? 'correct-answer'
            : answered && option === selectedAnswer && option !== question.correct_answer
            ? 'wrong-answer'
            : selectedAnswer === option
            ? 'border-blue-500 bg-blue-50'
            : ''
        ]"
      >
        {{ option }}
      </button>
    </div>

    <!-- 填空題 - 基於參考設計的緊湊佈局 -->
    <div v-else-if="question.type === 'fill_blank'" class="mb-6">
      <input
        v-model="userInput"
        @keyup.enter="submitAnswer"
        :disabled="answered"
        type="text"
        placeholder="請輸入答案..."
        class="w-full p-3 border-2 rounded-lg focus:border-blue-500 focus:outline-none text-center text-lg"
        :class="[
          answered && isCorrect
            ? 'border-green-500 bg-green-50'
            : answered && !isCorrect
            ? 'border-red-500 bg-red-50'
            : 'border-gray-300'
        ]"
      />

      <!-- 正確答案顯示 -->
      <div v-if="answered && !isCorrect" class="mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm text-center">
        <span class="text-green-800">正確答案: {{ question.correct_answer }}</span>
      </div>
    </div>

    <!-- 拼寫練習 - 基於參考設計的緊湊佈局 -->
    <div v-else-if="question.type === 'typing'" class="mb-6">
      <input
        v-model="userInput"
        @keyup.enter="submitAnswer"
        :disabled="answered"
        type="text"
        placeholder="請拼寫英文單詞..."
        class="w-full p-3 border-2 rounded-lg focus:border-blue-500 focus:outline-none text-center text-lg"
        :class="[
          answered && isCorrect
            ? 'border-green-500 bg-green-50'
            : answered && !isCorrect
            ? 'border-red-500 bg-red-50'
            : 'border-gray-300'
        ]"
      />

      <!-- 額外信息 -->
      <div v-if="question.definition" class="mt-2 text-sm text-gray-600 text-center">
        <strong>定義:</strong> {{ question.definition }}
      </div>

      <!-- 正確答案顯示 -->
      <div v-if="answered && !isCorrect" class="mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm text-center">
        <span class="text-green-800">正確答案: {{ question.correct_answer }}</span>
      </div>
    </div>

    <!-- 答案反饋 - 增強的即時反饋 -->
    <div v-if="answered" class="text-center mb-4">
      <div class="inline-flex items-center px-4 py-2 rounded-lg mb-3"
           :class="isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
        <span class="text-lg mr-2">{{ isCorrect ? '🎉' : '❌' }}</span>
        <span class="font-semibold">
          {{ isCorrect ? '回答正確！' : '回答錯誤' }}
        </span>
      </div>

      <!-- 詳細反饋信息 -->
      <div class="text-sm space-y-2 bg-slate-50 p-3 rounded-lg">
        <div class="flex justify-center items-center space-x-4 text-gray-600">
          <span>⏱️ 用時: {{ elapsedTime }}秒</span>
          <span v-if="isCorrect" class="text-green-600">
            📈 +{{ getScoreForType(question.type) }}分
          </span>
          <span v-else class="text-red-600">
            📉 -5分
          </span>
        </div>
        <div v-if="question.explanation" class="text-gray-600 italic text-center">
          💡 {{ question.explanation }}
        </div>
      </div>
    </div>

    <!-- 操作按鈕 - 基於參考設計的居中佈局 -->
    <div class="text-center">
      <button
        v-if="!answered && (question.type !== 'multiple_choice')"
        @click="submitAnswer"
        :disabled="!canSubmit"
        class="bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-8 rounded-lg shadow-sm"
      >
        提交答案
      </button>

      <button
        v-if="answered"
        @click="nextQuestion"
        class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg shadow-sm"
      >
        下一題
      </button>

      <!-- 計時器 - 小字顯示 -->
      <div v-if="startTime" class="mt-2 text-xs text-gray-500">
        用時: {{ elapsedTime }}秒
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  question: {
    type: Object,
    required: true
  },
  questionNumber: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['answer', 'next-question'])

// 響應式數據
const selectedAnswer = ref('')
const userInput = ref('')
const answered = ref(false)
const isCorrect = ref(false)
const startTime = ref(null)
const elapsedTime = ref(0)
let timer = null

// 計算屬性
const canSubmit = computed(() => {
  if (props.question.type === 'multiple_choice') {
    return selectedAnswer.value !== ''
  } else {
    return userInput.value.trim() !== ''
  }
})

// 監聽問題變化，重置狀態
watch(() => props.question, (newQuestion) => {
  if (newQuestion) {
    // 重置所有狀態
    selectedAnswer.value = ''
    userInput.value = ''
    answered.value = false
    isCorrect.value = false
    elapsedTime.value = 0

    // 重新開始計時
    startTimer()
  }
}, { immediate: true })

// 方法
function selectOption(option) {
  if (answered.value) return
  
  selectedAnswer.value = option
  setTimeout(() => {
    submitAnswer()
  }, 500) // 延遲提交，讓用戶看到選擇效果
}

function submitAnswer() {
  if (answered.value || !canSubmit.value) return
  
  answered.value = true
  stopTimer()
  
  let userAnswer = ''
  let correct = false
  
  if (props.question.type === 'multiple_choice') {
    userAnswer = selectedAnswer.value
    correct = selectedAnswer.value === props.question.correct_answer
  } else {
    userAnswer = userInput.value.trim()
    correct = userAnswer.toLowerCase() === props.question.correct_answer.toLowerCase()
  }
  
  isCorrect.value = correct
  
  // 發送答案給父組件
  emit('answer', {
    answer: userAnswer,
    isCorrect: correct,
    timeSpent: elapsedTime.value
  })
}

function nextQuestion() {
  emit('next-question')
}

function startTimer() {
  startTime.value = Date.now()
  timer = setInterval(() => {
    elapsedTime.value = Math.floor((Date.now() - startTime.value) / 1000)
  }, 1000)
}

function stopTimer() {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

// 根據練習類型獲取分數
function getScoreForType(type) {
  const scores = {
    'multiple_choice': 10,
    'fill_blank': 12,
    'typing': 15
  }
  return scores[type] || 10
}

// 生命週期
onMounted(() => {
  startTimer()
})

onUnmounted(() => {
  stopTimer()
})
</script>

<style scoped>
.question-container {
  max-width: 100%;
}

.btn-primary {
  @apply px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed;
}
</style>
