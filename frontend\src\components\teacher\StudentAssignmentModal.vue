<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl h-full max-h-[90vh] flex flex-col">
      <!-- 標題區域 - 固定高度 -->
      <div class="flex justify-between items-center p-6 border-b border-gray-200 flex-shrink-0">
        <h3 class="text-lg font-medium text-gray-900">
          分配詞彙列表：{{ list.name }}
        </h3>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 主要內容區域 - 可滾動 -->
      <div class="flex-1 overflow-hidden p-6">
        <div class="h-full flex flex-col">

          <!-- 載入狀態 -->
          <div v-if="loading" class="flex justify-center items-center h-full">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>

          <!-- 錯誤狀態 -->
          <div v-else-if="error" class="flex flex-col justify-center items-center h-full">
            <p class="text-red-600 mb-4">{{ error }}</p>
            <button @click="fetchData" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
              重新載入
            </button>
          </div>

          <!-- 主要內容 -->
          <div v-else class="h-full flex flex-col space-y-4">
            <!-- 搜索和篩選 - 固定區域 -->
            <div class="flex flex-col sm:flex-row gap-4 flex-shrink-0">
              <div class="flex-1">
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="搜索學生姓名或學號..."
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
              </div>
              <div class="flex gap-2">
                <select
                  v-model="selectedGrade"
                  class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">所有年級</option>
                  <option value="S1">中一</option>
                  <option value="S2">中二</option>
                  <option value="S3">中三</option>
                  <option value="S4">中四</option>
                  <option value="S5">中五</option>
                  <option value="S6">中六</option>
                </select>
                <button
                  @click="toggleSelectAll"
                  class="px-4 py-2 text-sm font-medium text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50"
                >
                  {{ allSelected ? '取消全選' : '全選' }}
                </button>
              </div>
            </div>

            <!-- 學生列表 - 可滾動區域 -->
            <div class="flex-1 border border-gray-200 rounded-lg overflow-hidden flex flex-col">
              <div class="bg-gray-50 px-4 py-3 border-b border-gray-200 flex-shrink-0">
                <div class="flex justify-between items-center">
                  <h4 class="text-sm font-medium text-gray-900">
                    學生列表 ({{ filteredStudents.length }} 人)
                  </h4>
                  <div class="text-sm text-gray-600">
                    已選擇: {{ selectedStudents.length }} 人
                  </div>
                </div>
              </div>

              <div class="flex-1 overflow-y-auto">
              <div v-for="student in filteredStudents" :key="student.id" class="border-b border-gray-100 last:border-b-0">
                <label class="flex items-center px-4 py-3 hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    :value="student.id"
                    v-model="selectedStudents"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  >
                  <div class="ml-3 flex-1">
                    <div class="flex justify-between items-center">
                      <div>
                        <div class="text-sm font-medium text-gray-900">{{ student.full_name }}</div>
                        <div class="text-sm text-gray-500">{{ student.username }} - {{ student.grade }}</div>
                      </div>
                      <div class="text-right">
                        <div v-if="student.is_assigned" class="text-xs text-green-600 font-medium">
                          已分配
                        </div>
                        <div v-if="student.assignment_date" class="text-xs text-gray-500">
                          {{ formatDate(student.assignment_date) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </label>
              </div>
            </div>

            <!-- 空狀態 -->
            <div v-if="filteredStudents.length === 0" class="text-center py-8">
              <div class="text-gray-400 mb-2">
                <svg class="mx-auto h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <p class="text-gray-500">沒有找到符合條件的學生</p>
            </div>
          </div>

            <!-- 分配設置 - 固定區域 -->
            <div class="bg-gray-50 p-4 rounded-lg flex-shrink-0">
              <h5 class="text-sm font-medium text-gray-900 mb-3">分配設置</h5>
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">截止日期</label>
                  <input
                    v-model="assignmentSettings.dueDate"
                    type="date"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">狀態</label>
                  <select
                    v-model="assignmentSettings.status"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="active">啟用</option>
                    <option value="inactive">停用</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按鈕 - 固定底部 -->
      <div class="flex justify-end space-x-3 p-6 border-t border-gray-200 flex-shrink-0">
        <button
          @click="$emit('close')"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
        >
          取消
        </button>
        <button
          @click="saveAssignments"
          :disabled="saving || selectedStudents.length === 0"
          class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {{ saving ? '保存中...' : `分配給 ${selectedStudents.length} 個學生` }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'

export default {
  name: 'StudentAssignmentModal',
  props: {
    list: {
      type: Object,
      required: true
    }
  },
  emits: ['close', 'updated'],
  setup(props, { emit }) {
    const authStore = useAuthStore()
    
    const loading = ref(true)
    const error = ref('')
    const saving = ref(false)
    const students = ref([])
    const selectedStudents = ref([])
    const searchQuery = ref('')
    const selectedGrade = ref('')
    
    const assignmentSettings = ref({
      dueDate: '',
      status: 'active'
    })

    const filteredStudents = computed(() => {
      let filtered = students.value
      
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(student => 
          student.full_name.toLowerCase().includes(query) ||
          student.username.toLowerCase().includes(query)
        )
      }
      
      if (selectedGrade.value) {
        filtered = filtered.filter(student => student.grade === selectedGrade.value)
      }
      
      return filtered
    })

    const allSelected = computed(() => {
      return filteredStudents.value.length > 0 && 
             filteredStudents.value.every(student => selectedStudents.value.includes(student.id))
    })

    const fetchData = async () => {
      try {
        loading.value = true
        error.value = ''
        
        // 獲取所有學生和當前分配狀態
        const response = await fetch(`/api/teacher/vocabulary-lists/${props.list.id}/students`, {
          headers: {
            'Authorization': `Bearer ${authStore.token}`
          }
        })
        
        if (!response.ok) {
          throw new Error('載入學生資料失敗')
        }
        
        const data = await response.json()
        students.value = data.students || []
        
        // 預選已分配的學生
        selectedStudents.value = students.value
          .filter(student => student.is_assigned)
          .map(student => student.id)
        
      } catch (err) {
        console.error('載入學生資料錯誤:', err)
        error.value = err.message || '載入學生資料失敗'
      } finally {
        loading.value = false
      }
    }

    const toggleSelectAll = () => {
      if (allSelected.value) {
        // 取消選擇當前篩選的學生
        const filteredIds = filteredStudents.value.map(s => s.id)
        selectedStudents.value = selectedStudents.value.filter(id => !filteredIds.includes(id))
      } else {
        // 選擇當前篩選的學生
        const filteredIds = filteredStudents.value.map(s => s.id)
        const newSelections = filteredIds.filter(id => !selectedStudents.value.includes(id))
        selectedStudents.value = [...selectedStudents.value, ...newSelections]
      }
    }

    const saveAssignments = async () => {
      try {
        saving.value = true
        
        const response = await fetch(`/api/teacher/vocabulary-lists/${props.list.id}/assign`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${authStore.token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            student_ids: selectedStudents.value,
            due_date: assignmentSettings.value.dueDate || null,
            status: assignmentSettings.value.status
          })
        })
        
        if (!response.ok) {
          throw new Error('分配詞彙列表失敗')
        }
        
        emit('updated')
        emit('close')
        
      } catch (err) {
        console.error('分配詞彙列表錯誤:', err)
        error.value = err.message || '分配詞彙列表失敗'
      } finally {
        saving.value = false
      }
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-TW')
    }

    // 設置默認截止日期（一個月後）
    const setDefaultDueDate = () => {
      const oneMonthLater = new Date()
      oneMonthLater.setMonth(oneMonthLater.getMonth() + 1)
      assignmentSettings.value.dueDate = oneMonthLater.toISOString().split('T')[0]
    }

    onMounted(() => {
      fetchData()
      setDefaultDueDate()
    })

    return {
      loading,
      error,
      saving,
      students,
      selectedStudents,
      searchQuery,
      selectedGrade,
      assignmentSettings,
      filteredStudents,
      allSelected,
      fetchData,
      toggleSelectAll,
      saveAssignments,
      formatDate
    }
  }
}
</script>
