<template>
  <div class="bg-white p-6 rounded-lg shadow-md">
    <h3 class="text-lg font-medium text-gray-900 mb-4">上傳詞彙列表</h3>
    
    <!-- 上傳說明 -->
    <div class="mb-6 p-4 bg-blue-50 rounded-lg">
      <h4 class="text-sm font-medium text-blue-900 mb-2">上傳格式說明</h4>
      <ul class="text-sm text-blue-800 space-y-1">
        <li>• 請上傳包含以下內容的 ZIP 文件：</li>
        <li>• <code>data.json</code> - 詞彙數據文件</li>
        <li>• <code>images/</code> - 圖片文件夾（可選）</li>
        <li>• JSON 文件格式請參考範例模板</li>
      </ul>
      <div class="mt-3">
        <button
          @click="downloadTemplate"
          class="text-sm text-blue-600 hover:text-blue-800 underline"
        >
          下載範例模板
        </button>
      </div>
    </div>

    <!-- 文件上傳區域 -->
    <div class="mb-6">
      <div
        @drop="handleDrop"
        @dragover.prevent
        @dragenter.prevent
        class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors"
        :class="{ 'border-blue-400 bg-blue-50': isDragging }"
      >
        <div v-if="!selectedFile">
          <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
          <div class="mt-4">
            <label for="file-upload" class="cursor-pointer">
              <span class="mt-2 block text-sm font-medium text-gray-900">
                拖拽 ZIP 文件到此處，或
                <span class="text-blue-600 hover:text-blue-500">點擊選擇文件</span>
              </span>
            </label>
            <input
              id="file-upload"
              name="file-upload"
              type="file"
              accept=".zip"
              class="sr-only"
              @change="handleFileSelect"
            >
            <p class="mt-1 text-xs text-gray-500">僅支持 ZIP 格式，最大 50MB</p>
          </div>
        </div>
        
        <div v-else class="space-y-3">
          <div class="flex items-center justify-center">
            <svg class="h-8 w-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-900">{{ selectedFile.name }}</p>
            <p class="text-xs text-gray-500">{{ formatFileSize(selectedFile.size) }}</p>
          </div>
          <button
            @click="clearFile"
            class="text-sm text-red-600 hover:text-red-800"
          >
            移除文件
          </button>
        </div>
      </div>
    </div>

    <!-- 列表信息 -->
    <div class="mb-6 space-y-4">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">列表名稱</label>
        <input
          v-model="listInfo.name"
          type="text"
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="輸入詞彙列表名稱"
        >
      </div>
      
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
        <textarea
          v-model="listInfo.description"
          rows="3"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="輸入列表描述"
        ></textarea>
      </div>
      
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">年級</label>
          <select
            v-model="listInfo.grade"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">選擇年級</option>
            <option value="S1">中一</option>
            <option value="S2">中二</option>
            <option value="S3">中三</option>
            <option value="S4">中四</option>
            <option value="S5">中五</option>
            <option value="S6">中六</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">科目</label>
          <input
            v-model="listInfo.subject"
            type="text"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="如：基礎詞彙"
          >
        </div>
      </div>
    </div>

    <!-- 上傳進度 -->
    <div v-if="uploading" class="mb-6">
      <div class="flex justify-between text-sm text-gray-600 mb-2">
        <span>上傳進度</span>
        <span>{{ uploadProgress }}%</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div
          class="bg-blue-600 h-2 rounded-full transition-all duration-300"
          :style="{ width: uploadProgress + '%' }"
        ></div>
      </div>
    </div>

    <!-- 錯誤信息 -->
    <div v-if="error" class="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
      <div class="flex">
        <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">上傳失敗</h3>
          <p class="mt-1 text-sm text-red-700">{{ error }}</p>
        </div>
      </div>
    </div>

    <!-- 成功信息 -->
    <div v-if="success" class="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
      <div class="flex">
        <svg class="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-green-800">上傳成功</h3>
          <p class="mt-1 text-sm text-green-700">詞彙列表已成功創建</p>
        </div>
      </div>
    </div>

    <!-- 操作按鈕 -->
    <div class="flex justify-end space-x-3">
      <button
        @click="$emit('cancel')"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
        :disabled="uploading"
      >
        取消
      </button>
      <button
        @click="uploadFile"
        :disabled="!canUpload"
        class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
      >
        {{ uploading ? '上傳中...' : '開始上傳' }}
      </button>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

export default {
  name: 'VocabularyListUpload',
  emits: ['success', 'cancel'],
  setup(props, { emit }) {
    const authStore = useAuthStore()
    
    const selectedFile = ref(null)
    const isDragging = ref(false)
    const uploading = ref(false)
    const uploadProgress = ref(0)
    const error = ref('')
    const success = ref(false)
    
    const listInfo = ref({
      name: '',
      description: '',
      grade: '',
      subject: ''
    })

    const canUpload = computed(() => {
      return selectedFile.value && 
             listInfo.value.name && 
             listInfo.value.grade && 
             listInfo.value.subject && 
             !uploading.value
    })

    const handleDrop = (e) => {
      e.preventDefault()
      isDragging.value = false
      
      const files = e.dataTransfer.files
      if (files.length > 0) {
        handleFile(files[0])
      }
    }

    const handleFileSelect = (e) => {
      const files = e.target.files
      if (files.length > 0) {
        handleFile(files[0])
      }
    }

    const handleFile = (file) => {
      if (!file.name.toLowerCase().endsWith('.zip')) {
        error.value = '請選擇 ZIP 格式的文件'
        return
      }
      
      if (file.size > 50 * 1024 * 1024) { // 50MB
        error.value = '文件大小不能超過 50MB'
        return
      }
      
      selectedFile.value = file
      error.value = ''
      success.value = false
    }

    const clearFile = () => {
      selectedFile.value = null
      error.value = ''
      success.value = false
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const uploadFile = async () => {
      if (!canUpload.value) return
      
      try {
        uploading.value = true
        uploadProgress.value = 0
        error.value = ''
        
        const formData = new FormData()
        formData.append('zipFile', selectedFile.value)
        formData.append('name', listInfo.value.name)
        formData.append('description', listInfo.value.description)
        formData.append('grade', listInfo.value.grade)
        formData.append('subject', listInfo.value.subject)
        
        const xhr = new XMLHttpRequest()
        
        // 監聽上傳進度
        xhr.upload.addEventListener('progress', (e) => {
          if (e.lengthComputable) {
            uploadProgress.value = Math.round((e.loaded / e.total) * 100)
          }
        })
        
        // 處理響應
        xhr.addEventListener('load', () => {
          if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText)
            if (response.success) {
              success.value = true
              setTimeout(() => {
                emit('success', response.data)
              }, 1500)
            } else {
              error.value = response.message || '上傳失敗'
            }
          } else {
            error.value = '上傳失敗，請重試'
          }
          uploading.value = false
        })
        
        xhr.addEventListener('error', () => {
          error.value = '網絡錯誤，請重試'
          uploading.value = false
        })
        
        // 發送請求
        xhr.open('POST', '/api/teacher/vocabulary-lists/upload')
        xhr.setRequestHeader('Authorization', `Bearer ${authStore.token}`)
        xhr.send(formData)
        
      } catch (err) {
        console.error('上傳錯誤:', err)
        error.value = err.message || '上傳失敗'
        uploading.value = false
      }
    }

    const downloadTemplate = () => {
      // 創建範例數據
      const templateData = {
        vocabularies: [
          {
            text: "example",
            phonetic: "/ɪɡˈzæmpəl/",
            part_of_speech: "noun",
            definition_en: "a thing characteristic of its kind or illustrating a general rule",
            translation_zh_tw: "例子",
            sentence_en: "This is a good example of modern art.",
            image_path: "images/example.jpg",
            grade: "S1"
          }
        ]
      }
      
      // 下載 JSON 文件
      const blob = new Blob([JSON.stringify(templateData, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'vocabulary-template.json'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }

    return {
      selectedFile,
      isDragging,
      uploading,
      uploadProgress,
      error,
      success,
      listInfo,
      canUpload,
      handleDrop,
      handleFileSelect,
      clearFile,
      formatFileSize,
      uploadFile,
      downloadTemplate
    }
  }
}
</script>
