import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 路由組件
import LoginView from '@/views/LoginView.vue'
import StudentLayout from '@/views/student/StudentLayout.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/login'
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      meta: { requiresGuest: true }
    },
    {
      path: '/student',
      name: 'student-layout',
      component: StudentLayout,
      meta: { requiresAuth: true, roles: ['student'] },
      children: [
        {
          path: '',
          name: 'student-dashboard',
          component: () => import('@/views/student/DashboardView.vue')
        },
        {
          path: 'vocabulary-lists',
          name: 'student-vocabulary-lists',
          component: () => import('@/views/student/VocabularyListsView.vue')
        },
        {
          path: 'study',
          name: 'student-study',
          component: () => import('@/views/student/StudyView.vue')
        },
        {
          path: 'practice',
          name: 'student-practice',
          component: () => import('@/views/student/PracticeView.vue')
        },
        {
          path: 'progress',
          name: 'student-progress',
          component: () => import('@/views/student/ProgressView.vue')
        },
        {
          path: 'review',
          name: 'student-review',
          component: () => import('@/views/student/ReviewView.vue')
        }
      ]
    },
    {
      path: '/teacher',
      name: 'teacher-layout',
      component: () => import('@/views/teacher/TeacherLayout.vue'),
      meta: { requiresAuth: true, roles: ['teacher'] },
      children: [
        {
          path: '',
          name: 'teacher-dashboard',
          component: () => import('@/views/teacher/DashboardView.vue')
        },
        {
          path: 'vocabulary-lists',
          name: 'teacher-vocabulary-lists',
          component: () => import('@/views/teacher/VocabularyListsManagementView.vue')
        },
        {
          path: 'student-progress',
          name: 'teacher-student-progress',
          component: () => import('@/views/teacher/StudentProgressView.vue')
        },
        {
          path: 'student-progress/:studentId',
          name: 'teacher-student-detail-progress',
          component: () => import('@/views/teacher/StudentDetailProgressView.vue')
        }
      ]
    },
    {
      path: '/admin',
      name: 'admin-layout',
      component: () => import('@/views/admin/AdminLayout.vue'),
      meta: { requiresAuth: true, roles: ['admin'] },
      children: [
        {
          path: '',
          name: 'admin-dashboard',
          component: () => import('@/views/admin/DashboardView.vue')
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('@/views/NotFoundView.vue')
    }
  ]
})

// 路由守衛
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 檢查是否需要認證
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      next('/login')
      return
    }
    
    // 檢查角色權限
    if (to.meta.roles && !to.meta.roles.includes(authStore.userRole)) {
      // 重定向到用戶對應的首頁
      next(`/${authStore.userRole}`)
      return
    }
  }
  
  // 檢查是否需要訪客狀態（如登入頁面）
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    next(`/${authStore.userRole}`)
    return
  }
  
  next()
})

export default router
