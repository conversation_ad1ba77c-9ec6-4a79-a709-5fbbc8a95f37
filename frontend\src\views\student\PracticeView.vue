<template>
  <!-- 練習界面 - 基於參考設計的完全無滾動佈局 -->
  <div class="h-full flex flex-col overflow-hidden">
    <!-- 練習設置 - 完全無滾動設計，使用 min-h-screen 確保完整顯示 -->
    <div v-if="!practiceStarted && !showResults" class="min-h-full flex flex-col justify-center items-center p-4">
      <div class="w-full max-w-md mx-auto">
        <!-- 標題區域 - 緊湊設計 -->
        <div class="text-center mb-6">
          <h2 class="text-xl md:text-2xl font-bold text-slate-900 mb-2">
            {{ isReviewMode ? '錯誤詞彙複習' : '練習測驗' }}
          </h2>
          <p class="text-sm text-slate-600">
            {{ isReviewMode ? '複習您經常答錯的詞彙，加強記憶' : '通過練習鞏固您的詞彙知識' }}
          </p>
        </div>

        <!-- 練習類型選擇 - 緊湊設計 -->
        <div class="mb-6">
          <h3 class="text-base font-semibold mb-3">選擇練習類型</h3>
          <div class="space-y-2">
            <button
              v-for="type in practiceTypes"
              :key="type.value"
              @click="selectedType = type.value"
              :class="[
                'w-full p-3 border-2 rounded-lg text-left transition-colors',
                selectedType === type.value
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              ]"
            >
              <div class="font-medium text-sm">{{ type.name }}</div>
              <div class="text-xs text-gray-600 mt-1">{{ type.description }}</div>
            </button>
          </div>
        </div>

        <!-- 題目數量設置 - 緊湊設計 -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            題目數量: {{ questionCount }}
          </label>
          <input
            v-model="questionCount"
            type="range"
            min="3"
            max="10"
            class="w-full"
          />
        </div>

        <!-- 開始按鈕 - 緊湊設計 -->
        <button
          @click="startPractice"
          :disabled="isLoading"
          class="w-full btn-primary py-3 text-sm font-semibold"
        >
          <span v-if="isLoading">載入中...</span>
          <span v-else>開始練習</span>
        </button>
      </div>
    </div>

    <!-- 練習進行中 - 基於參考設計的完全無滾動佈局，使用 min-h-screen -->
    <div v-if="practiceStarted && !showResults" class="min-h-full flex flex-col justify-center">
      <div class="w-full max-w-2xl mx-auto px-4">
        <!-- 進度條 - 緊湊設計 -->
        <div class="mb-4">
          <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium text-gray-600">進度</span>
            <span class="text-sm text-gray-500">
              {{ currentQuestionIndex + 1 }} / {{ questions.length }}
            </span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }"
            ></div>
          </div>
        </div>

        <!-- 當前題目 - 使用固定高度容器，確保完整顯示 -->
        <div v-if="currentQuestion" class="bg-white rounded-xl shadow-lg border border-slate-200">
          <QuestionComponent
            :question="currentQuestion"
            :questionNumber="currentQuestionIndex + 1"
            @answer="handleAnswer"
            @next-question="handleNextQuestion"
          />
        </div>
      </div>
    </div>

    <!-- 結果頁面 - 基於參考設計的居中佈局 -->
    <div v-if="showResults" class="min-h-full flex flex-col justify-center items-center p-4">
      <div class="w-full max-w-md mx-auto text-center">
        <h2 class="text-2xl font-bold mb-4">練習完成！</h2>
        <p class="text-lg mb-2">你的成績是:</p>
        <p class="text-5xl font-bold text-blue-600 mb-8">{{ results.accuracy }}%</p>

        <div class="grid grid-cols-2 gap-4 mb-6">
          <div class="bg-blue-50 p-4 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">{{ results.total_questions }}</div>
            <div class="text-sm text-gray-600">總題數</div>
          </div>
          <div class="bg-green-50 p-4 rounded-lg">
            <div class="text-2xl font-bold text-green-600">{{ results.correct_answers }}</div>
            <div class="text-sm text-gray-600">答對</div>
          </div>
        </div>

        <button
          @click="resetPractice"
          class="w-full bg-slate-800 hover:bg-slate-900 text-white font-bold py-3 rounded-lg shadow-sm"
        >
          再次學習
        </button>
      </div>
    </div>

    <!-- 錯誤提示 -->
    <div v-if="error" class="card bg-red-50 border-red-200">
      <div class="text-red-800">
        <strong>錯誤:</strong> {{ error }}
      </div>
      <button @click="error = null" class="mt-2 text-red-600 hover:text-red-800">
        關閉
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRoute } from 'vue-router'
import QuestionComponent from '@/components/practice/QuestionComponent.vue'

const authStore = useAuthStore()
const route = useRoute()

// 響應式數據
const isLoading = ref(false)
const error = ref(null)
const practiceStarted = ref(false)
const showResults = ref(false)
const selectedType = ref('multiple_choice')
const questionCount = ref(5)
const questions = ref([])
const currentQuestionIndex = ref(0)
const answers = ref([])
const results = ref({})
const vocabularyUpdates = ref([])
const practiceSessionId = ref('')

// 練習類型配置
const practiceTypes = [
  {
    value: 'multiple_choice',
    name: '選擇題',
    description: '從選項中選擇正確答案'
  },
  {
    value: 'fill_blank',
    name: '填空題',
    description: '根據句子填入正確單詞'
  },
  {
    value: 'typing',
    name: '拼寫練習',
    description: '根據中文意思拼寫英文'
  }
]

// 計算屬性
const currentQuestion = computed(() => {
  return questions.value[currentQuestionIndex.value] || null
})

const isReviewMode = computed(() => {
  return route.query.mode === 'review'
})

// 方法
async function startPractice() {
  isLoading.value = true
  error.value = null

  try {
    const params = new URLSearchParams({
      type: selectedType.value,
      count: questionCount.value.toString()
    })

    if (isReviewMode.value) {
      params.append('mode', 'review')
    }

    // 添加詞彙列表ID支持
    const listId = route.query.listId
    if (listId) {
      params.append('listId', listId)
    }

    const response = await fetch(`/api/practice/questions?${params}`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: 載入練習題目失敗`)
    }

    const result = await response.json()

    if (!result.success) {
      throw new Error(result.message || '載入練習題目失敗')
    }

    questions.value = result.data.questions
    practiceSessionId.value = result.data.practice_session_id
    practiceStarted.value = true
    currentQuestionIndex.value = 0
    answers.value = []

  } catch (err) {
    console.error('開始練習失敗:', err)
    error.value = err.message || '開始練習失敗，請重試'
  } finally {
    isLoading.value = false
  }
}

function handleAnswer(answerData) {
  // 記錄答案
  answers.value.push({
    question_id: currentQuestion.value.id,
    vocabulary_id: currentQuestion.value.vocabulary_id,
    user_answer: answerData.answer,
    is_correct: answerData.isCorrect,
    time_spent: answerData.timeSpent || 0
  })

  // 不在這裡自動移動到下一題，等待用戶點擊
}

function handleNextQuestion() {
  // 移動到下一題或結束練習
  if (currentQuestionIndex.value < questions.value.length - 1) {
    currentQuestionIndex.value++
  } else {
    finishPractice()
  }
}

async function finishPractice() {
  isLoading.value = true

  try {
    const response = await fetch('/api/practice/submit', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        practice_session_id: practiceSessionId.value,
        answers: answers.value
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: 提交練習結果失敗`)
    }

    const result = await response.json()

    if (!result.success) {
      throw new Error(result.message || '提交練習結果失敗')
    }

    results.value = result.data.session_results
    vocabularyUpdates.value = result.data.vocabulary_updates || []
    showResults.value = true
    practiceStarted.value = false

  } catch (err) {
    console.error('提交練習結果失敗:', err)
    error.value = err.message || '提交練習結果失敗，請重試'
  } finally {
    isLoading.value = false
  }
}

function resetPractice() {
  practiceStarted.value = false
  showResults.value = false
  questions.value = []
  answers.value = []
  currentQuestionIndex.value = 0
  results.value = {}
  vocabularyUpdates.value = []
  error.value = null
}

function getStatusColor(status) {
  const colors = {
    'assigned': 'bg-gray-100 text-gray-800',
    'learning': 'bg-blue-100 text-blue-800',
    'mastered': 'bg-green-100 text-green-800',
    'review': 'bg-yellow-100 text-yellow-800'
  }
  return colors[status] || 'bg-gray-100 text-gray-800'
}

function getStatusText(status) {
  const texts = {
    'assigned': '已分配',
    'learning': '學習中',
    'mastered': '已掌握',
    'review': '需複習'
  }
  return texts[status] || '未知'
}

onMounted(() => {
  // 組件掛載時的初始化邏輯
})
</script>
