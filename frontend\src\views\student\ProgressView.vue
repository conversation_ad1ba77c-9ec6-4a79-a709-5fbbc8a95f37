<template>
  <div class="p-6">
    <div class="mb-6">
      <h2 class="text-2xl font-bold text-slate-900">學習進度</h2>
      <p class="text-slate-600">查看您的學習統計和進度</p>
    </div>

    <!-- 載入狀態 -->
    <div v-if="isLoading" class="text-center py-8">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <p class="mt-2 text-slate-600">載入進度數據中...</p>
    </div>

    <!-- 錯誤狀態 -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <p class="text-red-600">{{ error }}</p>
      <button @click="loadProgressData" class="mt-2 text-red-600 hover:text-red-800 underline">
        重新載入
      </button>
    </div>

    <!-- 進度數據 -->
    <div v-else class="space-y-6">
      <!-- 總體統計 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-blue-50 p-6 rounded-lg border border-blue-200">
          <div class="text-3xl font-bold text-blue-600">{{ overallStats.total_vocabularies }}</div>
          <div class="text-sm text-blue-800">總詞彙數</div>
        </div>
        <div class="bg-green-50 p-6 rounded-lg border border-green-200">
          <div class="text-3xl font-bold text-green-600">{{ overallStats.mastered_count }}</div>
          <div class="text-sm text-green-800">已掌握</div>
        </div>
        <div class="bg-yellow-50 p-6 rounded-lg border border-yellow-200">
          <div class="text-3xl font-bold text-yellow-600">{{ overallStats.learning_count }}</div>
          <div class="text-sm text-yellow-800">學習中</div>
        </div>
        <div class="bg-purple-50 p-6 rounded-lg border border-purple-200">
          <div class="text-3xl font-bold text-purple-600">{{ overallStats.overall_accuracy }}%</div>
          <div class="text-sm text-purple-800">總體準確率</div>
        </div>
      </div>

      <!-- 學習進度圖表 -->
      <div class="bg-white p-6 rounded-lg border border-slate-200">
        <h3 class="text-lg font-semibold text-slate-900 mb-4">最近7天練習活動</h3>
        <div v-if="recentActivity.length === 0" class="text-center text-slate-500 py-8">
          暫無練習記錄
        </div>
        <div v-else class="space-y-3">
          <div v-for="activity in recentActivity" :key="activity.date"
               class="flex items-center justify-between p-3 bg-slate-50 rounded">
            <div class="flex items-center space-x-3">
              <div class="text-sm font-medium text-slate-900">{{ formatDate(activity.date) }}</div>
              <div class="text-sm text-slate-600">{{ activity.practices }} 次練習</div>
            </div>
            <div class="text-sm font-medium" :class="getAccuracyColor(activity.accuracy)">
              {{ Math.round(activity.accuracy) }}% 準確率
            </div>
          </div>
        </div>
      </div>

      <!-- 詞彙狀態分佈 -->
      <div class="bg-white p-6 rounded-lg border border-slate-200">
        <h3 class="text-lg font-semibold text-slate-900 mb-4">詞彙學習狀態</h3>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-slate-700">學習進度</span>
            <span class="text-slate-900 font-medium">{{ Math.round(learningProgress) }}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-3">
            <div class="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-500"
                 :style="{ width: `${learningProgress}%` }"></div>
          </div>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
            <div class="text-center">
              <div class="text-lg font-bold text-gray-600">{{ overallStats.assigned_count }}</div>
              <div class="text-xs text-gray-500">待學習</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-blue-600">{{ overallStats.learning_count }}</div>
              <div class="text-xs text-blue-500">學習中</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-green-600">{{ overallStats.mastered_count }}</div>
              <div class="text-xs text-green-500">已掌握</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-yellow-600">{{ reviewCount }}</div>
              <div class="text-xs text-yellow-500">需複習</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// 響應式數據
const isLoading = ref(false)
const error = ref(null)
const overallStats = ref({
  total_vocabularies: 0,
  mastered_count: 0,
  learning_count: 0,
  assigned_count: 0,
  overall_accuracy: 0
})
const recentActivity = ref([])

// 計算屬性
const learningProgress = computed(() => {
  const total = overallStats.value.total_vocabularies
  const mastered = overallStats.value.mastered_count
  return total > 0 ? (mastered / total) * 100 : 0
})

const reviewCount = computed(() => {
  // 假設需要複習的詞彙數量，實際應該從API獲取
  return Math.max(0, overallStats.value.total_vocabularies -
                     overallStats.value.mastered_count -
                     overallStats.value.learning_count -
                     overallStats.value.assigned_count)
})

// 方法
async function loadProgressData() {
  isLoading.value = true
  error.value = null

  try {
    const response = await fetch('/api/student/statistics', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: 載入進度數據失敗`)
    }

    const result = await response.json()

    if (!result.success) {
      throw new Error(result.message || '載入進度數據失敗')
    }

    overallStats.value = result.data.overall_stats
    recentActivity.value = result.data.recent_activity || []

  } catch (err) {
    console.error('載入進度數據失敗:', err)
    error.value = err.message || '載入進度數據失敗，請重試'
  } finally {
    isLoading.value = false
  }
}

function formatDate(dateString) {
  const date = new Date(dateString)
  const today = new Date()
  const diffTime = today - date
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 0) return '今天'
  if (diffDays === 1) return '昨天'
  if (diffDays < 7) return `${diffDays}天前`

  return date.toLocaleDateString('zh-TW', {
    month: 'short',
    day: 'numeric'
  })
}

function getAccuracyColor(accuracy) {
  if (accuracy >= 80) return 'text-green-600'
  if (accuracy >= 60) return 'text-yellow-600'
  return 'text-red-600'
}

// 生命週期
onMounted(() => {
  loadProgressData()
})
</script>
