<template>
  <div class="p-6">
    <div class="mb-6">
      <h2 class="text-2xl font-bold text-slate-900">錯誤詞彙複習</h2>
      <p class="text-slate-600">複習您經常答錯的詞彙，加強記憶</p>
    </div>

    <!-- 載入狀態 -->
    <div v-if="isLoading" class="text-center py-8">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <p class="mt-2 text-slate-600">載入複習詞彙中...</p>
    </div>

    <!-- 錯誤狀態 -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <p class="text-red-600">{{ error }}</p>
      <button @click="loadReviewVocabularies" class="mt-2 text-red-600 hover:text-red-800 underline">
        重新載入
      </button>
    </div>

    <!-- 無需複習詞彙 -->
    <div v-else-if="reviewVocabularies.length === 0" class="text-center py-12">
      <div class="text-6xl mb-4">🎉</div>
      <h3 class="text-xl font-semibold text-slate-900 mb-2">太棒了！</h3>
      <p class="text-slate-600">目前沒有需要複習的詞彙</p>
      <button @click="$router.push('/student/practice')" 
              class="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg">
        開始新的練習
      </button>
    </div>

    <!-- 複習詞彙列表 -->
    <div v-else class="space-y-6">
      <!-- 複習統計 -->
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-yellow-800">需要複習的詞彙</h3>
            <p class="text-yellow-700">共 {{ reviewVocabularies.length }} 個詞彙需要加強練習</p>
          </div>
          <button @click="startReviewPractice" 
                  class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg">
            開始複習練習
          </button>
        </div>
      </div>

      <!-- 詞彙列表 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div v-for="vocab in reviewVocabularies" :key="vocab.id" 
             class="bg-white border border-slate-200 rounded-lg p-4 hover:shadow-md transition-shadow">
          <div class="flex items-start justify-between mb-3">
            <div>
              <h4 class="text-lg font-semibold text-slate-900">{{ vocab.text }}</h4>
              <p v-if="vocab.phonetic" class="text-sm text-slate-500">{{ vocab.phonetic }}</p>
            </div>
            <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">
              需複習
            </span>
          </div>
          
          <div class="space-y-2 text-sm">
            <div>
              <span class="font-medium text-slate-700">中文:</span>
              <span class="text-slate-600">{{ vocab.translation_zh_tw }}</span>
            </div>
            <div>
              <span class="font-medium text-slate-700">準確率:</span>
              <span :class="getAccuracyColor(vocab.accuracy)">
                {{ Math.round(vocab.accuracy) }}%
              </span>
            </div>
            <div>
              <span class="font-medium text-slate-700">練習次數:</span>
              <span class="text-slate-600">{{ vocab.practice_count }}</span>
            </div>
          </div>

          <!-- 進度條 -->
          <div class="mt-3">
            <div class="flex justify-between text-xs text-slate-500 mb-1">
              <span>學習進度</span>
              <span>{{ vocab.progress }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-red-500 h-2 rounded-full transition-all duration-300"
                   :style="{ width: `${vocab.progress}%` }"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 複習建議 -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 class="text-lg font-semibold text-blue-800 mb-2">💡 複習建議</h3>
        <ul class="text-blue-700 space-y-1 text-sm">
          <li>• 建議每天複習 3-5 個詞彙，避免一次性複習太多</li>
          <li>• 重點關注準確率低於 60% 的詞彙</li>
          <li>• 可以結合拼寫練習來加強記憶</li>
          <li>• 複習時注意詞彙的使用情境和例句</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'

const authStore = useAuthStore()
const router = useRouter()

// 響應式數據
const isLoading = ref(false)
const error = ref(null)
const reviewVocabularies = ref([])

// 方法
async function loadReviewVocabularies() {
  isLoading.value = true
  error.value = null

  try {
    const response = await fetch('/api/student/vocabularies?status=review&low_accuracy=true', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: 載入複習詞彙失敗`)
    }

    const result = await response.json()

    if (!result.success) {
      throw new Error(result.message || '載入複習詞彙失敗')
    }

    // 篩選需要複習的詞彙（準確率低於70%或進度低於30%）
    reviewVocabularies.value = result.data.vocabularies.filter(vocab => {
      const accuracy = vocab.practice_count > 0 ? (vocab.correct_count / vocab.practice_count) * 100 : 0
      return accuracy < 70 || vocab.progress < 30
    }).map(vocab => ({
      ...vocab,
      accuracy: vocab.practice_count > 0 ? (vocab.correct_count / vocab.practice_count) * 100 : 0
    }))

  } catch (err) {
    console.error('載入複習詞彙失敗:', err)
    error.value = err.message || '載入複習詞彙失敗，請重試'
  } finally {
    isLoading.value = false
  }
}

function getAccuracyColor(accuracy) {
  if (accuracy >= 70) return 'text-green-600'
  if (accuracy >= 50) return 'text-yellow-600'
  return 'text-red-600'
}

function startReviewPractice() {
  // 跳轉到練習頁面，並設置為複習模式
  router.push('/student/practice?mode=review')
}

// 生命週期
onMounted(() => {
  loadReviewVocabularies()
})
</script>
