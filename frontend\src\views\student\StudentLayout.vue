<template>
  <!-- 響應式學生布局 - 基於參考設計 -->
  <div class="h-screen bg-slate-50 flex flex-col">
    <!-- 頂部導航 - 響應式 -->
    <header class="bg-white shadow-sm border-b border-slate-200 flex-shrink-0">
      <div class="px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- 左側：Logo + 漢堡菜單 -->
          <div class="flex items-center">
            <!-- 手機版漢堡菜單按鈕 -->
            <button
              @click="toggleMobileMenu"
              class="md:hidden p-2 rounded-md text-slate-500 hover:text-slate-700 hover:bg-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <!-- Logo 和標題 -->
            <h1 class="text-lg sm:text-xl font-semibold text-slate-900 ml-2 md:ml-0">英語詞彙學習</h1>
            <span class="hidden sm:inline-block ml-3 px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded">
              {{ authStore.user?.full_name }}
            </span>
          </div>

          <!-- 右側：用戶信息 + 登出 -->
          <div class="flex items-center space-x-2">
            <!-- 手機版用戶名 -->
            <span class="sm:hidden px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
              {{ authStore.user?.full_name }}
            </span>

            <button
              @click="handleLogout"
              class="text-slate-500 hover:text-slate-700 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              登出
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要內容區域 - 響應式 -->
    <div class="flex-1 flex overflow-hidden relative">
      <!-- 側邊導航 - 響應式 -->
      <nav
        :class="[
          'bg-white shadow-lg border-r border-slate-200 transition-transform duration-300 ease-in-out',
          'md:relative md:translate-x-0 md:z-auto md:shadow-sm',
          isMobileMenuOpen
            ? 'fixed inset-y-0 left-0 w-64 translate-x-0 z-50'
            : 'fixed inset-y-0 left-0 w-64 -translate-x-full md:w-64 md:z-auto'
        ]"
      >
        <!-- 手機版關閉按鈕 -->
        <div class="md:hidden flex justify-end p-4">
          <button
            @click="closeMobileMenu"
            class="p-2 rounded-md text-slate-500 hover:text-slate-700 hover:bg-slate-100"
          >
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- 導航菜單 -->
        <div class="p-4 pt-0 md:pt-4">
          <ul class="space-y-2">
            <li>
              <router-link
                to="/student"
                @click="closeMobileMenu"
                class="flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors"
                :class="$route.name === 'student-dashboard'
                  ? 'bg-blue-100 text-blue-700 border-l-4 border-blue-500'
                  : 'text-slate-600 hover:bg-slate-100 hover:text-slate-900'"
              >
                <span class="text-lg mr-3">📊</span>
                學習概況
              </router-link>
            </li>
            <li>
              <router-link
                to="/student/vocabulary-lists"
                @click="closeMobileMenu"
                class="flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors"
                :class="['student-vocabulary-lists', 'student-study'].includes($route.name)
                  ? 'bg-blue-100 text-blue-700 border-l-4 border-blue-500'
                  : 'text-slate-600 hover:bg-slate-100 hover:text-slate-900'"
              >
                <span class="text-lg mr-3">📚</span>
                詞彙學習
              </router-link>
            </li>
            <li>
              <router-link
                to="/student/practice"
                @click="closeMobileMenu"
                class="flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors"
                :class="$route.name === 'student-practice'
                  ? 'bg-blue-100 text-blue-700 border-l-4 border-blue-500'
                  : 'text-slate-600 hover:bg-slate-100 hover:text-slate-900'"
              >
                <span class="text-lg mr-3">✏️</span>
                練習測驗
              </router-link>
            </li>
            <li>
              <router-link
                to="/student/review"
                @click="closeMobileMenu"
                class="flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors"
                :class="$route.name === 'student-review'
                  ? 'bg-blue-100 text-blue-700 border-l-4 border-blue-500'
                  : 'text-slate-600 hover:bg-slate-100 hover:text-slate-900'"
              >
                <span class="text-lg mr-3">🔄</span>
                錯誤複習
              </router-link>
            </li>
            <li>
              <router-link
                to="/student/progress"
                @click="closeMobileMenu"
                class="flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors"
                :class="$route.name === 'student-progress'
                  ? 'bg-blue-100 text-blue-700 border-l-4 border-blue-500'
                  : 'text-slate-600 hover:bg-slate-100 hover:text-slate-900'"
              >
                <span class="text-lg mr-3">📈</span>
                學習進度
              </router-link>
            </li>
          </ul>
        </div>
      </nav>

      <!-- 主要內容 - 響應式，無滾動 -->
      <main
        class="flex-1 overflow-hidden"
        @click="isMobileMenuOpen && closeMobileMenu()"
      >
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 手機菜單狀態
const isMobileMenuOpen = ref(false)

function toggleMobileMenu() {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

function closeMobileMenu() {
  isMobileMenuOpen.value = false
}

// 處理窗口大小變化
function handleResize() {
  if (window.innerWidth >= 768) { // md breakpoint
    isMobileMenuOpen.value = false
  }
}

async function handleLogout() {
  await authStore.logout()
  router.push('/login')
}

// 生命週期
onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>
