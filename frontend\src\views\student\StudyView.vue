<template>
  <!-- 響應式詞彙學習頁面 - 基於參考設計 -->
  <div class="h-full flex flex-col p-4 sm:p-6">
    <!-- 頂部導航區域 -->
    <div class="flex-shrink-0 flex justify-between items-center mb-4">
      <div>
        <div class="flex items-center space-x-2 mb-1">
          <button
            @click="goBackToLists"
            class="p-1 rounded-md hover:bg-gray-100 transition-colors"
          >
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h2 class="text-xl sm:text-2xl font-bold text-slate-900">
            {{ currentList ? currentList.name : '詞彙學習' }}
          </h2>
        </div>
        <p class="text-slate-600 text-sm sm:text-base">
          {{ currentList ? currentList.description : '學習分配給您的詞彙' }}
        </p>
      </div>
      <div class="flex items-center space-x-2">
        <span v-if="currentList" class="hidden sm:inline bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
          {{ currentList.grade }} - {{ currentList.subject }}
        </span>
        <span class="hidden sm:inline bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
          {{ currentIndex + 1 }}/{{ totalWords }}
        </span>
      </div>
    </div>

    <!-- 詞彙卡片區域 - 響應式 -->
    <div class="flex-grow flex flex-col min-h-0 justify-center">
      <div v-if="currentWord" class="w-full max-w-4xl mx-auto bg-white rounded-xl shadow-lg border border-slate-200 p-4 sm:p-6 flex flex-col md:flex-row min-h-0 mb-4">
        <!-- 左側區域：單詞+語音+圖片 (平板以上) -->
        <div class="flex flex-col md:w-1/2 md:pr-6">
          <!-- 單詞和語音按鈕區域 -->
          <div class="flex-shrink-0 relative">
            <div class="flex items-start justify-between md:block">
              <div class="flex-grow min-w-0 mr-2 md:mr-12 md:pr-0">
                <h3 class="text-3xl sm:text-4xl font-bold text-slate-900 break-all whitespace-nowrap">
                  {{ currentWord.word }}
                </h3>
                <p class="text-base sm:text-lg text-slate-500 mt-1 break-words">
                  {{ currentWord.phonetic }}
                </p>
              </div>
              <button
                @click="speakWord"
                class="flex-shrink-0 p-2 rounded-full hover:bg-slate-100 active:bg-slate-200 transition-colors md:absolute md:top-0 md:right-0"
              >
                <svg class="w-6 h-6 text-slate-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M19.114 5.636a9 9 0 010 12.728M16.463 8.288a5.25 5.25 0 010 7.424M6.75 8.25l4.72-4.72a.75.75 0 011.28.53v15.88a.75.75 0 01-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.01 9.01 0 012.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75z" />
                </svg>
              </button>
            </div>
          </div>

          <!-- 圖片區域 - 響應式 -->
          <div class="w-full flex-shrink-0 flex items-center justify-center mt-3 md:mt-4 h-48 md:h-40 lg:h-48 bg-slate-100 rounded-lg">
            <div v-if="currentWord.image_url" class="w-full h-full">
              <img
                :src="currentWord.image_url"
                :alt="currentWord.word"
                class="w-full h-full object-contain rounded-lg"
              />
            </div>
            <div v-else class="text-slate-400 text-center">
              <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              <p class="text-sm">暫無圖片</p>
            </div>
          </div>
        </div>

        <!-- 右側區域：文字內容 (平板以上) / 下方區域 (手機) -->
        <div class="flex flex-col justify-center h-full mt-4 md:mt-0 md:w-1/2 md:pl-6 text-sm overflow-y-auto subtle-scrollbar">
          <div class="space-y-3">
            <div>
              <p class="font-semibold text-slate-900 mb-1">中文解釋:</p>
              <p class="text-slate-700">{{ currentWord.translation_zh || '暫無中文解釋' }}</p>
            </div>
            <div>
              <p class="font-semibold text-slate-900 mb-1">英文解釋:</p>
              <p class="text-slate-700">{{ currentWord.definition_en || '暫無英文解釋' }}</p>
            </div>
            <div>
              <p class="font-semibold text-slate-900 mb-1">例句:</p>
              <p class="text-slate-700">{{ currentWord.sentence_en || '暫無例句' }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 載入狀態 -->
      <div v-else-if="isLoading" class="w-full max-w-4xl mx-auto bg-white rounded-xl shadow-lg border border-slate-200 p-8 text-center">
        <div class="loading-spinner mx-auto mb-4"></div>
        <p class="text-slate-600">載入詞彙中...</p>
      </div>

      <!-- 無詞彙狀態 -->
      <div v-else class="w-full max-w-4xl mx-auto bg-white rounded-xl shadow-lg border border-slate-200 p-8 text-center">
        <div class="text-slate-400 mb-4">
          <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-slate-900 mb-2">暫無詞彙</h3>
        <p class="text-slate-600">請聯繫老師分配詞彙給您</p>
      </div>

      <!-- 控制按鈕 - 響應式 -->
      <div v-if="currentWord" class="flex-shrink-0 max-w-2xl mx-auto w-full px-4">
        <div class="flex items-center gap-3">
          <button
            @click="previousWord"
            :disabled="currentIndex === 0"
            class="flex-1 bg-white border border-slate-300 hover:bg-slate-100 disabled:bg-slate-50 disabled:text-slate-400 text-slate-800 font-semibold py-3 rounded-lg shadow-sm transition-colors"
          >
            ← 上一個
          </button>
          <div class="flex-shrink-0">
            <span class="bg-blue-500 text-white px-3 py-2 rounded-full text-sm font-bold shadow-sm">
              {{ currentIndex + 1 }}/{{ totalWords }}
            </span>
          </div>
          <button
            @click="nextWord"
            :disabled="currentIndex === totalWords - 1"
            class="flex-1 bg-white border border-slate-300 hover:bg-slate-100 disabled:bg-slate-50 disabled:text-slate-400 text-slate-800 font-semibold py-3 rounded-lg shadow-sm transition-colors"
          >
            下一個 →
          </button>
        </div>
        <button
          @click="startPractice"
          class="mt-4 w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 rounded-lg shadow-sm transition-colors transform hover:scale-105"
        >
          開始練習！
        </button>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 響應式數據
const isLoading = ref(true)
const error = ref(null)
const vocabularies = ref([])
const currentIndex = ref(0)
const currentList = ref(null)

// 計算屬性
const currentWord = computed(() => {
  return vocabularies.value[currentIndex.value] || null
})

const totalWords = computed(() => {
  return vocabularies.value.length
})

const currentGrade = computed(() => {
  return authStore.user?.grade || 'S1'
})

// 方法
async function loadVocabularies() {
  isLoading.value = true
  error.value = null

  try {
    const listId = route.query.listId
    if (!listId) {
      // 如果沒有列表ID，重定向到列表選擇頁面
      router.push('/student/vocabulary-lists')
      return
    }

    const response = await fetch(`/api/student/vocabulary-lists/${listId}/vocabularies`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: 載入詞彙失敗`)
    }

    const result = await response.json()

    if (!result.success) {
      throw new Error(result.message || '載入詞彙失敗')
    }

    // 設置當前列表信息
    currentList.value = result.data.list

    // 轉換API數據格式以匹配現有組件結構
    vocabularies.value = result.data.vocabularies.map(vocab => ({
      id: vocab.id,
      word: vocab.text,
      phonetic: vocab.phonetic,
      translation_zh: vocab.translation_zh_tw,
      definition_en: vocab.definition_en,
      sentence_en: vocab.sentence_en,
      image_url: getImageUrl(vocab),
      grade: vocab.grade || 'S1',
      // 新增學習狀態信息
      status: vocab.status,
      progress: vocab.progress,
      practice_count: vocab.practice_count,
      correct_count: vocab.correct_count
    }))

    // 更新統計信息（如果需要顯示）
    if (result.data.stats) {
      console.log('學習統計:', result.data.stats)
    }

  } catch (err) {
    console.error('載入詞彙失敗:', err)
    error.value = err.message || '載入詞彙失敗，請重試'
  } finally {
    isLoading.value = false
  }
}

// 處理圖片URL的輔助函數
function getImageUrl(vocabulary) {
  if (vocabulary.image_path && vocabulary.image_path !== "") {
    // 指向後端服務器的圖片路徑
    return `http://localhost:3000/uploads/${vocabulary.image_path}`
  } else {
    // 使用佔位圖片
    const word = encodeURIComponent(vocabulary.text)
    return `https://via.placeholder.com/300x200/4F46E5/FFFFFF?text=${word}`
  }
}

function previousWord() {
  if (currentIndex.value > 0) {
    currentIndex.value--
  }
}

function nextWord() {
  if (currentIndex.value < totalWords.value - 1) {
    currentIndex.value++
  }
}

function speakWord() {
  if (currentWord.value && 'speechSynthesis' in window) {
    const utterance = new SpeechSynthesisUtterance(currentWord.value.word)
    utterance.lang = 'en-US'
    utterance.rate = 0.8
    speechSynthesis.speak(utterance)
  }
}

function startPractice() {
  const listId = route.query.listId
  if (listId) {
    router.push({
      path: '/student/practice',
      query: { listId }
    })
  } else {
    router.push('/student/practice')
  }
}

function goBackToLists() {
  router.push('/student/vocabulary-lists')
}

// 監聽路由變化
watch(() => route.query.listId, (newListId) => {
  if (newListId) {
    loadVocabularies()
  }
}, { immediate: false })

// 生命週期
onMounted(() => {
  loadVocabularies()
})
</script>
