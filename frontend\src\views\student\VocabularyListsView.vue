<template>
  <div class="h-full flex flex-col">
    <!-- 頁面標題 -->
    <div class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <h1 class="text-2xl font-bold text-gray-900">詞彙學習</h1>
        <p class="mt-2 text-sm text-gray-600">選擇詞彙列表開始學習</p>
      </div>
    </div>

    <!-- 主要內容 -->
    <div class="flex-1 overflow-y-auto min-h-0">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 載入狀態 -->
        <div v-if="loading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>

        <!-- 錯誤狀態 -->
        <div v-else-if="error" class="text-center py-12">
          <div class="text-red-600 mb-4">
            <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">載入失敗</h3>
          <p class="text-gray-600 mb-4">{{ error }}</p>
          <button @click="fetchVocabularyLists" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            重新載入
          </button>
        </div>

        <!-- 詞彙列表 -->
        <div v-else-if="vocabularyLists.length > 0" class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <div
            v-for="list in vocabularyLists"
            :key="list.id"
            class="bg-white rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-shadow duration-200 cursor-pointer"
            @click="selectVocabularyList(list)"
          >
            <div class="p-6">
              <!-- 列表標題和描述 -->
              <div class="mb-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ list.name }}</h3>
                <p class="text-sm text-gray-600">{{ list.description }}</p>
              </div>

              <!-- 列表信息 -->
              <div class="space-y-2 mb-4">
                <div class="flex items-center text-sm text-gray-500">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253z" />
                  </svg>
                  {{ list.total_words }} 個詞彙
                </div>
                <div class="flex items-center text-sm text-gray-500">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                  {{ list.grade }} - {{ list.subject }}
                </div>
              </div>

              <!-- 進度條 -->
              <div class="mb-4">
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                  <span>學習進度</span>
                  <span>{{ list.progress_percentage }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    :style="{ width: `${list.progress_percentage}%` }"
                  ></div>
                </div>
              </div>

              <!-- 狀態標籤 -->
              <div class="flex items-center justify-between">
                <span 
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getStatusClass(list.status)"
                >
                  {{ getStatusText(list.status) }}
                </span>
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- 空狀態 -->
        <div v-else class="text-center py-12">
          <div class="text-gray-400 mb-4">
            <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253z" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">暫無詞彙列表</h3>
          <p class="text-gray-600">請聯繫老師為您分配詞彙列表</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

export default {
  name: 'VocabularyListsView',
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    
    const loading = ref(true)
    const error = ref('')
    const vocabularyLists = ref([])

    const fetchVocabularyLists = async () => {
      try {
        loading.value = true
        error.value = ''
        
        const response = await fetch('/api/student/vocabulary-lists', {
          headers: {
            'Authorization': `Bearer ${authStore.token}`
          }
        })
        
        if (!response.ok) {
          throw new Error('載入詞彙列表失敗')
        }
        
        const data = await response.json()
        console.log('API響應數據:', data) // 調試用

        if (data.success && data.data && data.data.lists) {
          vocabularyLists.value = data.data.lists
        } else {
          vocabularyLists.value = data.lists || []
        }
        
      } catch (err) {
        console.error('載入詞彙列表錯誤:', err)
        error.value = err.message || '載入詞彙列表失敗'
      } finally {
        loading.value = false
      }
    }

    const selectVocabularyList = (list) => {
      // 導航到詞彙學習頁面，傳遞列表ID
      router.push({
        name: 'student-study',
        query: { listId: list.id }
      })
    }

    const getStatusClass = (status) => {
      switch (status) {
        case 'active':
          return 'bg-green-100 text-green-800'
        case 'completed':
          return 'bg-blue-100 text-blue-800'
        case 'archived':
          return 'bg-gray-100 text-gray-800'
        default:
          return 'bg-gray-100 text-gray-800'
      }
    }

    const getStatusText = (status) => {
      switch (status) {
        case 'active':
          return '進行中'
        case 'completed':
          return '已完成'
        case 'archived':
          return '已歸檔'
        default:
          return '未知'
      }
    }

    onMounted(() => {
      fetchVocabularyLists()
    })

    return {
      loading,
      error,
      vocabularyLists,
      fetchVocabularyLists,
      selectVocabularyList,
      getStatusClass,
      getStatusText
    }
  }
}
</script>
