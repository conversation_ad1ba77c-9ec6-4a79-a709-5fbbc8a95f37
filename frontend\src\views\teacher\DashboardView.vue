<template>
  <div class="h-full overflow-y-auto">
    <div class="p-4 sm:p-6 lg:p-8">
      <!-- 標題區域 -->
      <div class="mb-6 sm:mb-8">
        <h2 class="text-2xl sm:text-3xl font-bold text-slate-900">教師儀表板</h2>
        <p class="text-slate-600 mt-1">歡迎回來，{{ authStore.user?.full_name }}老師！</p>
      </div>

      <!-- 統計卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
        <!-- 詞彙列表數量 -->
        <div class="bg-white rounded-lg sm:rounded-xl shadow-md border border-slate-200 p-4 sm:p-6">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg mr-3">
              <span class="text-lg sm:text-2xl">📚</span>
            </div>
            <div>
              <p class="text-sm font-medium text-slate-600">詞彙列表</p>
              <p class="text-xl sm:text-2xl font-bold text-slate-900">{{ stats.totalLists }}</p>
            </div>
          </div>
        </div>

        <!-- 總詞彙數 -->
        <div class="bg-white rounded-lg sm:rounded-xl shadow-md border border-slate-200 p-4 sm:p-6">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg mr-3">
              <span class="text-lg sm:text-2xl">📝</span>
            </div>
            <div>
              <p class="text-sm font-medium text-slate-600">總詞彙數</p>
              <p class="text-xl sm:text-2xl font-bold text-slate-900">{{ stats.totalWords }}</p>
            </div>
          </div>
        </div>

        <!-- 分配給學生 -->
        <div class="bg-white rounded-lg sm:rounded-xl shadow-md border border-slate-200 p-4 sm:p-6">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg mr-3">
              <span class="text-lg sm:text-2xl">👥</span>
            </div>
            <div>
              <p class="text-sm font-medium text-slate-600">分配給學生</p>
              <p class="text-xl sm:text-2xl font-bold text-slate-900">{{ stats.assignedStudents }}</p>
            </div>
          </div>
        </div>

        <!-- 活躍列表 -->
        <div class="bg-white rounded-lg sm:rounded-xl shadow-md border border-slate-200 p-4 sm:p-6">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg mr-3">
              <span class="text-lg sm:text-2xl">⚡</span>
            </div>
            <div>
              <p class="text-sm font-medium text-slate-600">活躍列表</p>
              <p class="text-xl sm:text-2xl font-bold text-slate-900">{{ stats.activeLists }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 快速操作卡片 -->
        <div class="bg-white rounded-lg shadow-md border border-slate-200 p-6">
          <h3 class="text-lg font-semibold text-slate-900 mb-4">快速操作</h3>
          <div class="space-y-3">
            <router-link
              to="/teacher/vocabulary-lists"
              class="flex items-center p-3 rounded-lg border border-slate-200 hover:bg-slate-50 transition-colors"
            >
              <span class="text-2xl mr-3">📚</span>
              <div>
                <p class="font-medium text-slate-900">管理詞彙列表</p>
                <p class="text-sm text-slate-600">創建、編輯和分配詞彙列表</p>
              </div>
            </router-link>
          </div>
        </div>

        <!-- 最近活動 -->
        <div class="bg-white rounded-lg shadow-md border border-slate-200 p-6">
          <h3 class="text-lg font-semibold text-slate-900 mb-4">最近活動</h3>
          <div v-if="recentActivity.length > 0" class="space-y-3">
            <div
              v-for="activity in recentActivity"
              :key="activity.id"
              class="flex items-start p-3 rounded-lg bg-slate-50"
            >
              <span class="text-lg mr-3">{{ activity.icon }}</span>
              <div>
                <p class="text-sm font-medium text-slate-900">{{ activity.title }}</p>
                <p class="text-xs text-slate-600">{{ activity.time }}</p>
              </div>
            </div>
          </div>
          <div v-else class="text-center py-8">
            <p class="text-slate-500">暫無最近活動</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// 響應式數據
const stats = ref({
  totalLists: 0,
  totalWords: 0,
  assignedStudents: 0,
  activeLists: 0
})

const recentActivity = ref([])
const loading = ref(true)
const error = ref('')

// 獲取統計數據
const fetchStats = async () => {
  try {
    const response = await fetch('/api/teacher/vocabulary-lists', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })

    if (!response.ok) {
      throw new Error('獲取統計數據失敗')
    }

    const data = await response.json()

    if (data.success && data.lists) {
      const lists = data.lists
      stats.value = {
        totalLists: lists.length,
        totalWords: lists.reduce((sum, list) => sum + (list.total_words || 0), 0),
        assignedStudents: lists.reduce((sum, list) => sum + (list.assigned_students || 0), 0),
        activeLists: lists.filter(list => list.assigned_students > 0).length
      }

      // 生成最近活動（基於列表創建時間）
      recentActivity.value = lists
        .slice(0, 3)
        .map(list => ({
          id: list.id,
          icon: '📚',
          title: `創建了詞彙列表「${list.name}」`,
          time: formatTime(list.created_at)
        }))
    }

  } catch (err) {
    console.error('獲取統計數據錯誤:', err)
    error.value = err.message || '獲取統計數據失敗'
  } finally {
    loading.value = false
  }
}

// 格式化時間
const formatTime = (dateString) => {
  if (!dateString) return '未知時間'

  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now - date
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return '今天'
  } else if (diffDays === 1) {
    return '昨天'
  } else if (diffDays < 7) {
    return `${diffDays} 天前`
  } else {
    return date.toLocaleDateString('zh-TW')
  }
}

// 生命週期
onMounted(() => {
  fetchStats()
})
</script>
