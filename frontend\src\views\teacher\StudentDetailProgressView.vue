<template>
  <div class="h-full flex flex-col">
    <!-- 頁面標題 -->
    <div class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <button 
              @click="$router.back()"
              class="mr-4 p-2 text-gray-400 hover:text-gray-600"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <div>
              <h1 class="text-2xl font-bold text-gray-900">
                {{ studentData?.student?.full_name || '學生' }} 的學習進度
              </h1>
              <p class="mt-2 text-sm text-gray-600">詳細的學習進度和練習記錄</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要內容 -->
    <div class="flex-1 overflow-y-auto">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 載入狀態 -->
        <div v-if="loading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>

        <!-- 錯誤狀態 -->
        <div v-else-if="error" class="text-center py-12">
          <div class="text-red-600 mb-4">
            <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">載入失敗</h3>
          <p class="text-gray-600 mb-4">{{ error }}</p>
          <button @click="fetchStudentProgress" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            重新載入
          </button>
        </div>

        <!-- 學生詳細進度 -->
        <div v-else-if="studentData">
          <!-- 學生基本信息 -->
          <div class="bg-white shadow rounded-lg mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-medium text-gray-900">學生信息</h3>
            </div>
            <div class="px-6 py-4">
              <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <dt class="text-sm font-medium text-gray-500">姓名</dt>
                  <dd class="mt-1 text-sm text-gray-900">{{ studentData.student.full_name }}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">用戶名</dt>
                  <dd class="mt-1 text-sm text-gray-900">{{ studentData.student.username }}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">年級</dt>
                  <dd class="mt-1 text-sm text-gray-900">{{ studentData.student.grade }}</dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500">最後登入</dt>
                  <dd class="mt-1 text-sm text-gray-900">{{ formatDate(studentData.student.last_login) }}</dd>
                </div>
              </div>
            </div>
          </div>

          <!-- 統計概覽 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                      <span class="text-white text-sm font-medium">📚</span>
                    </div>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">分配列表</dt>
                      <dd class="text-lg font-medium text-gray-900">{{ studentData.totalStats.total_lists }}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                      <span class="text-white text-sm font-medium">✅</span>
                    </div>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">已掌握詞彙</dt>
                      <dd class="text-lg font-medium text-gray-900">{{ studentData.totalStats.mastered_vocabularies }}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                      <span class="text-white text-sm font-medium">🎯</span>
                    </div>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">練習次數</dt>
                      <dd class="text-lg font-medium text-gray-900">{{ studentData.totalStats.total_practices }}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                      <span class="text-white text-sm font-medium">📊</span>
                    </div>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">正確率</dt>
                      <dd class="text-lg font-medium text-gray-900">{{ studentData.totalStats.accuracy_rate }}%</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 詞彙列表進度 -->
          <div class="bg-white shadow rounded-lg mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-medium text-gray-900">詞彙列表進度</h3>
            </div>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      列表名稱
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      年級/科目
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      詞彙進度
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      練習統計
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      分配時間
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      狀態
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="list in studentData.listsProgress" :key="list.list_id" class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div class="text-sm font-medium text-gray-900">{{ list.list_name }}</div>
                        <div class="text-sm text-gray-500">{{ list.description }}</div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {{ list.list_grade }} - {{ list.subject }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">
                        已掌握: {{ list.mastered_count }}/{{ list.total_vocabularies }}
                      </div>
                      <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div 
                          class="bg-green-600 h-2 rounded-full" 
                          :style="{ width: (list.mastered_count / list.total_vocabularies * 100) + '%' }"
                        ></div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>練習: {{ list.total_practices }} 次</div>
                      <div>正確: {{ list.correct_practices }} 次</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ formatDate(list.assigned_at) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="getStatusClass(list.list_status)">
                        {{ getStatusText(list.list_status) }}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 最近練習記錄 -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-medium text-gray-900">最近練習記錄</h3>
            </div>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      詞彙
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      練習類型
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      結果
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      用時
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      時間
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="practice in studentData.recentPractices" :key="practice.id" class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div class="text-sm font-medium text-gray-900">{{ practice.word }}</div>
                        <div class="text-sm text-gray-500">{{ practice.translation_zh_tw }}</div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ getPracticeTypeText(practice.practice_type) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="practice.is_correct ? 'text-green-600' : 'text-red-600'">
                        {{ practice.is_correct ? '✅ 正確' : '❌ 錯誤' }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ practice.time_spent ? practice.time_spent + '秒' : '-' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ formatDateTime(practice.created_at) }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const authStore = useAuthStore()

// 響應式數據
const loading = ref(false)
const error = ref('')
const studentData = ref(null)

// 方法
const fetchStudentProgress = async () => {
  try {
    loading.value = true
    error.value = ''
    
    const studentId = route.params.studentId
    const response = await fetch(`/api/teacher/student-progress/${studentId}`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })
    
    const data = await response.json()
    
    if (data.success) {
      studentData.value = data.data
    } else {
      error.value = data.message || '載入學生進度失敗'
    }
  } catch (err) {
    error.value = '載入學生進度失敗'
    console.error('載入學生進度錯誤:', err)
  } finally {
    loading.value = false
  }
}

const formatDate = (dateString) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleDateString('zh-TW')
}

const formatDateTime = (dateString) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-TW')
}

const getStatusClass = (status) => {
  const classes = {
    'active': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800',
    'completed': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800',
    'archived': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800'
  }
  return classes[status] || classes.active
}

const getStatusText = (status) => {
  const texts = {
    'active': '進行中',
    'completed': '已完成',
    'archived': '已歸檔'
  }
  return texts[status] || '進行中'
}

const getPracticeTypeText = (type) => {
  const types = {
    'multiple_choice': '選擇題',
    'typing': '拼寫練習',
    'fill_blank': '填空題',
    'pronunciation': '發音練習'
  }
  return types[type] || type
}

// 生命週期
onMounted(() => {
  fetchStudentProgress()
})
</script>
