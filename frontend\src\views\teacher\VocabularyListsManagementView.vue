<template>
  <div class="h-full flex flex-col">
    <!-- 頁面標題 -->
    <div class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex justify-between items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">詞彙列表管理</h1>
            <p class="mt-2 text-sm text-gray-600">創建和管理詞彙列表，分配給學生</p>
          </div>
          <button
            @click="showCreateModal = true"
            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
          >
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            新增列表
          </button>
        </div>
      </div>
    </div>

    <!-- 主要內容 -->
    <div class="flex-1 overflow-y-auto">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 載入狀態 -->
        <div v-if="loading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>

        <!-- 錯誤狀態 -->
        <div v-else-if="error" class="text-center py-12">
          <div class="text-red-600 mb-4">
            <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">載入失敗</h3>
          <p class="text-gray-600 mb-4">{{ error }}</p>
          <button @click="fetchVocabularyLists" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            重新載入
          </button>
        </div>

        <!-- 詞彙列表表格 -->
        <div v-else class="bg-white shadow overflow-hidden sm:rounded-md">
          <div class="px-4 py-5 sm:p-6">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      列表名稱
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      年級/科目
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      詞彙數量
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      分配學生
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      創建時間
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="list in vocabularyLists" :key="list.id" class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div class="text-sm font-medium text-gray-900">{{ list.name }}</div>
                        <div class="text-sm text-gray-500">{{ list.description }}</div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {{ list.grade }} - {{ list.subject }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ list.total_words }} 個詞彙
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ list.assigned_students || 0 }} 個學生
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ formatDate(list.created_at) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div class="flex justify-end space-x-2">
                        <button
                          @click="manageAssignments(list)"
                          class="text-blue-600 hover:text-blue-900"
                        >
                          分配
                        </button>
                        <button
                          @click="editList(list)"
                          class="text-indigo-600 hover:text-indigo-900"
                        >
                          編輯
                        </button>
                        <button
                          @click="deleteList(list)"
                          class="text-red-600 hover:text-red-900"
                        >
                          刪除
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 空狀態 -->
            <div v-if="vocabularyLists.length === 0" class="text-center py-12">
              <div class="text-gray-400 mb-4">
                <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253z" />
                </svg>
              </div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">暫無詞彙列表</h3>
              <p class="text-gray-600 mb-4">開始創建您的第一個詞彙列表</p>
              <button
                @click="showCreateModal = true"
                class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                新增列表
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 創建詞彙列表模態框 -->
    <div v-if="showCreateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
        <!-- 標題 -->
        <div class="flex justify-between items-center p-6 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">
            上傳詞彙列表
          </h3>
          <button
            @click="closeModal"
            class="text-gray-400 hover:text-gray-600"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- 內容 -->
        <div class="p-6">
          <!-- 隱藏的文件輸入 -->
          <input
            ref="fileInput"
            type="file"
            accept=".zip"
            @change="handleFileSelect"
            class="hidden"
          />

          <!-- 上傳說明 -->
          <div class="mb-6">
            <h4 class="text-sm font-medium text-gray-900 mb-2">上傳要求：</h4>
            <ul class="text-sm text-gray-600 space-y-1">
              <li>• ZIP 文件包含圖片資料夾和 JSON 文件</li>
              <li>• JSON 文件包含詞彙數據</li>
              <li>• 圖片文件名需與詞彙對應</li>
            </ul>
          </div>

          <!-- 文件上傳區域 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">選擇 ZIP 文件</label>
            <div
              @drop="handleDrop"
              @dragover.prevent
              @dragenter.prevent
              class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors"
              :class="{ 'border-blue-400 bg-blue-50': isDragging }"
            >
              <input
                ref="fileInput"
                type="file"
                accept=".zip"
                @change="handleFileSelect"
                class="hidden"
              >

              <div v-if="!selectedFile">
                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                  <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <div class="mt-4">
                  <button
                    type="button"
                    @click="$refs.fileInput.click()"
                    class="text-blue-600 hover:text-blue-500 font-medium"
                  >
                    點擊選擇文件
                  </button>
                  <p class="text-gray-500">或拖拽 ZIP 文件到此處</p>
                </div>
              </div>

              <div v-else class="text-center">
                <svg class="mx-auto h-12 w-12 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div class="mt-2">
                  <p class="text-sm font-medium text-gray-900">{{ selectedFile.name }}</p>
                  <p class="text-xs text-gray-500">{{ formatFileSize(selectedFile.size) }}</p>
                </div>
                <button
                  type="button"
                  @click="clearFile"
                  class="mt-2 text-sm text-red-600 hover:text-red-500"
                >
                  移除文件
                </button>
              </div>
            </div>
          </div>

          <!-- 上傳進度 -->
          <div v-if="uploadProgress > 0 && uploadProgress < 100" class="mb-4">
            <div class="flex justify-between text-sm text-gray-600 mb-1">
              <span>上傳進度</span>
              <span>{{ uploadProgress }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div
                class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                :style="{ width: uploadProgress + '%' }"
              ></div>
            </div>
          </div>

          <!-- 錯誤信息 -->
          <div v-if="uploadError" class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p class="text-sm text-red-600">{{ uploadError }}</p>
          </div>
        </div>

        <!-- 操作按鈕 -->
        <div class="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            type="button"
            @click="closeModal"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
            :disabled="uploading"
          >
            取消
          </button>
          <button
            @click="uploadFile"
            :disabled="!selectedFile || uploading"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {{ uploading ? '上傳中...' : '上傳並創建' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 學生分配模態框 -->
    <StudentAssignmentModal
      v-if="showAssignmentModal"
      :list="selectedList"
      @close="showAssignmentModal = false"
      @updated="fetchVocabularyLists"
    />
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import StudentAssignmentModal from '@/components/teacher/StudentAssignmentModal.vue'

export default {
  name: 'VocabularyListsManagementView',
  components: {
    StudentAssignmentModal
  },
  setup() {
    const authStore = useAuthStore()
    
    const loading = ref(true)
    const error = ref('')
    const vocabularyLists = ref([])
    const showCreateModal = ref(false)
    const showAssignmentModal = ref(false)
    const selectedList = ref(null)
    const editingList = ref(null)
    const saving = ref(false)

    // 文件上傳相關
    const fileInput = ref(null)
    const selectedFile = ref(null)
    const uploading = ref(false)
    const uploadProgress = ref(0)
    const uploadError = ref('')
    const isDragging = ref(false)

    const listForm = ref({
      name: '',
      description: '',
      grade: '',
      subject: ''
    })

    const fetchVocabularyLists = async () => {
      try {
        loading.value = true
        error.value = ''
        
        const response = await fetch('/api/teacher/vocabulary-lists', {
          headers: {
            'Authorization': `Bearer ${authStore.token}`
          }
        })
        
        if (!response.ok) {
          throw new Error('載入詞彙列表失敗')
        }
        
        const data = await response.json()
        vocabularyLists.value = data.lists || []
        
      } catch (err) {
        console.error('載入詞彙列表錯誤:', err)
        error.value = err.message || '載入詞彙列表失敗'
      } finally {
        loading.value = false
      }
    }

    const saveList = async () => {
      try {
        saving.value = true
        
        const url = editingList.value 
          ? `/api/teacher/vocabulary-lists/${editingList.value.id}`
          : '/api/teacher/vocabulary-lists'
        
        const method = editingList.value ? 'PUT' : 'POST'
        
        const response = await fetch(url, {
          method,
          headers: {
            'Authorization': `Bearer ${authStore.token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(listForm.value)
        })
        
        if (!response.ok) {
          throw new Error('保存詞彙列表失敗')
        }
        
        await fetchVocabularyLists()
        closeModal()
        
      } catch (err) {
        console.error('保存詞彙列表錯誤:', err)
        error.value = err.message || '保存詞彙列表失敗'
      } finally {
        saving.value = false
      }
    }

    const editList = (list) => {
      editingList.value = list
      listForm.value = {
        name: list.name,
        description: list.description,
        grade: list.grade,
        subject: list.subject
      }
      showCreateModal.value = true
    }

    const deleteList = async (list) => {
      if (!confirm(`確定要刪除詞彙列表「${list.name}」嗎？`)) {
        return
      }
      
      try {
        const response = await fetch(`/api/teacher/vocabulary-lists/${list.id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${authStore.token}`
          }
        })
        
        if (!response.ok) {
          throw new Error('刪除詞彙列表失敗')
        }
        
        await fetchVocabularyLists()
        
      } catch (err) {
        console.error('刪除詞彙列表錯誤:', err)
        error.value = err.message || '刪除詞彙列表失敗'
      }
    }

    const manageAssignments = (list) => {
      selectedList.value = list
      showAssignmentModal.value = true
    }

    // 文件上傳相關函數
    const handleFileSelect = (event) => {
      const file = event.target.files[0]
      if (file && (file.type === 'application/zip' || file.type === 'application/x-zip-compressed' || file.name.toLowerCase().endsWith('.zip'))) {
        selectedFile.value = file
        uploadError.value = ''
      } else {
        uploadError.value = '請選擇 ZIP 文件'
      }
    }

    const handleDrop = (event) => {
      event.preventDefault()
      isDragging.value = false

      const files = event.dataTransfer.files
      if (files.length > 0) {
        const file = files[0]
        if (file.type === 'application/zip' || file.type === 'application/x-zip-compressed' || file.name.toLowerCase().endsWith('.zip')) {
          selectedFile.value = file
          uploadError.value = ''
        } else {
          uploadError.value = '請選擇 ZIP 文件'
        }
      }
    }

    const clearFile = () => {
      selectedFile.value = null
      uploadProgress.value = 0
      uploadError.value = ''
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const uploadFile = async () => {
      if (!selectedFile.value) return

      try {
        uploading.value = true
        uploadProgress.value = 0
        uploadError.value = ''

        const formData = new FormData()
        formData.append('zipFile', selectedFile.value)

        const xhr = new XMLHttpRequest()

        // 監聽上傳進度
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            uploadProgress.value = Math.round((event.loaded / event.total) * 100)
          }
        })

        // 處理響應
        xhr.addEventListener('load', async () => {
          if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText)
            if (response.success) {
              await fetchVocabularyLists()
              closeModal()
            } else {
              uploadError.value = response.message || '上傳失敗'
            }
          } else {
            uploadError.value = '上傳失敗，請重試'
          }
          uploading.value = false
        })

        xhr.addEventListener('error', () => {
          uploadError.value = '網絡錯誤，請重試'
          uploading.value = false
        })

        // 發送請求
        xhr.open('POST', '/api/teacher/vocabulary-lists/upload')
        xhr.setRequestHeader('Authorization', `Bearer ${authStore.token}`)
        xhr.send(formData)

      } catch (err) {
        console.error('上傳錯誤:', err)
        uploadError.value = err.message || '上傳失敗'
        uploading.value = false
      }
    }

    const closeModal = () => {
      showCreateModal.value = false
      editingList.value = null
      listForm.value = {
        name: '',
        description: '',
        grade: '',
        subject: ''
      }
      // 清理文件上傳狀態
      clearFile()
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-TW')
    }

    onMounted(() => {
      fetchVocabularyLists()
    })

    return {
      loading,
      error,
      vocabularyLists,
      showCreateModal,
      showAssignmentModal,
      selectedList,
      editingList,
      saving,
      listForm,
      // 文件上傳相關
      fileInput,
      selectedFile,
      uploading,
      uploadProgress,
      uploadError,
      isDragging,
      handleFileSelect,
      handleDrop,
      clearFile,
      formatFileSize,
      uploadFile,
      // 其他函數
      fetchVocabularyLists,
      saveList,
      editList,
      deleteList,
      manageAssignments,
      closeModal,
      formatDate
    }
  }
}
</script>
