# 詞彙列表 ZIP 文件模板

## 📁 文件結構

您的 ZIP 文件應該包含以下結構：

```
vocabulary_list.zip
├── vocabulary_template.json    # 詞彙數據文件
└── images/                     # 圖片資料夾
    ├── apple.jpg
    ├── book.jpg
    ├── cat.jpg
    ├── dog.jpg
    ├── elephant.jpg
    ├── fish.jpg
    ├── guitar.jpg
    ├── house.jpg
    ├── ice.jpg
    └── juice.jpg
```

## 📝 JSON 文件格式

### 基本信息
```json
{
  "name": "詞彙列表名稱",
  "description": "詞彙列表描述",
  "grade": "年級 (S1-S6)",
  "subject": "科目名稱",
  "vocabularies": [...]
}
```

### 詞彙項目格式
每個詞彙項目包含以下欄位：

```json
{
  "word": "英文單詞",
  "pronunciation": "音標 (可選)",
  "definition": "中文定義",
  "definition_en": "英文解釋 (可選)",
  "example": "例句 (可選)",
  "image": "對應的圖片文件名"
}
```

## 🖼️ 圖片要求

- **格式**：JPG, PNG, GIF
- **大小**：建議不超過 2MB 每張
- **命名**：必須與 JSON 中的 `image` 欄位完全一致
- **位置**：所有圖片必須放在 `images/` 資料夾中

## ✅ 必填欄位

### JSON 文件：
- `name` - 詞彙列表名稱
- `vocabularies` - 詞彙數組

### 詞彙項目：
- `word` - 英文單詞
- `definition` - 中文定義
- `image` - 圖片文件名

## 📋 使用步驟

1. **準備圖片**：
   - 收集所有詞彙對應的圖片
   - 重新命名圖片文件（如：apple.jpg, book.jpg）
   - 將所有圖片放入 `images/` 資料夾

2. **編輯 JSON 文件**：
   - 複製 `vocabulary_template.json`
   - 修改基本信息（name, description, grade, subject）
   - 更新詞彙列表，確保 `image` 欄位與實際圖片文件名一致

3. **創建 ZIP 文件**：
   - 選擇 JSON 文件和 images 資料夾
   - 壓縮成 ZIP 文件
   - 確保 ZIP 文件根目錄包含 JSON 文件和 images 資料夾

4. **上傳到系統**：
   - 在教師介面點擊"新增詞彙列表"
   - 選擇或拖拽您的 ZIP 文件
   - 點擊"上傳並創建"

## ⚠️ 常見問題

### 上傳失敗？
- 檢查 ZIP 文件大小是否超過 50MB
- 確認 JSON 文件格式正確
- 檢查圖片文件名是否與 JSON 中的 `image` 欄位一致

### 圖片顯示不出來？
- 確認圖片文件在 `images/` 資料夾中
- 檢查文件名大小寫是否一致
- 確認圖片格式為 JPG/PNG/GIF

### JSON 格式錯誤？
- 使用 JSON 驗證工具檢查語法
- 確認所有字符串都用雙引號包圍
- 檢查是否有多餘的逗號

## 💡 提示

- 建議先用少量詞彙測試上傳功能
- 圖片文件名建議使用英文，避免特殊字符
- 可以省略 `pronunciation`、`definition_en` 和 `example` 欄位
- `grade` 可選值：S1, S2, S3, S4, S5, S6
- 添加 `definition_en` 可以提供更豐富的學習內容
